(function() {
    var implementors = Object.fromEntries([["shredstream_decoder",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html\" title=\"trait core::cmp::PartialOrd\">PartialOrd</a> for <a class=\"struct\" href=\"shredstream_decoder/types/accounts/struct.Pubkey.html\" title=\"struct shredstream_decoder::types::accounts::Pubkey\">Pubkey</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[339]}