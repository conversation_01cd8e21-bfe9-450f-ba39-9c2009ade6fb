(function() {
    var implementors = Object.fromEntries([["shredstream_decoder",[["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"enum\" href=\"shredstream_decoder/types/messages/enum.VersionedMessage.html\" title=\"enum shredstream_decoder::types::messages::VersionedMessage\">VersionedMessage</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/accounts/struct.MessageAddressTableLookup.html\" title=\"struct shredstream_decoder::types::accounts::MessageAddressTableLookup\">MessageAddressTableLookup</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/accounts/struct.Pubkey.html\" title=\"struct shredstream_decoder::types::accounts::Pubkey\">Pubkey</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/entries/struct.Entry.html\" title=\"struct shredstream_decoder::types::entries::Entry\">Entry</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/entries/struct.ParsedEntry.html\" title=\"struct shredstream_decoder::types::entries::ParsedEntry\">ParsedEntry</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/instructions/struct.CompiledInstruction.html\" title=\"struct shredstream_decoder::types::instructions::CompiledInstruction\">CompiledInstruction</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/messages/struct.LegacyMessage.html\" title=\"struct shredstream_decoder::types::messages::LegacyMessage\">LegacyMessage</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/messages/struct.MessageHeader.html\" title=\"struct shredstream_decoder::types::messages::MessageHeader\">MessageHeader</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/messages/struct.V0Message.html\" title=\"struct shredstream_decoder::types::messages::V0Message\">V0Message</a>"],["impl&lt;'de&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"struct\" href=\"shredstream_decoder/types/transactions/struct.VersionedTransaction.html\" title=\"struct shredstream_decoder::types::transactions::VersionedTransaction\">VersionedTransaction</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[3622]}