<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src/lib.rs`."><title>lib.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="shredstream_decoder" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../static.files/storage-82c7156e.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">shredstream_decoder/</div>lib.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">pub mod </span>types;
<a href=#2 id=2 data-nosnippet>2</a>
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>wasm_bindgen::prelude::<span class="kw-2">*</span>;
<a href=#4 id=4 data-nosnippet>4</a>
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">pub use </span>types::<span class="kw-2">*</span>;
<a href=#6 id=6 data-nosnippet>6</a>
<a href=#7 id=7 data-nosnippet>7</a><span class="attr">#[wasm_bindgen]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">pub fn </span>decode_entries(slot: u64, bytes: <span class="kw-2">&amp;</span>[u8]) -&gt; <span class="prelude-ty">Result</span>&lt;ParsedEntry, JsValue&gt; {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">if </span>bytes.is_empty() {
<a href=#10 id=10 data-nosnippet>10</a>        <span class="kw">return </span><span class="prelude-val">Err</span>(JsValue::from_str(<span class="string">"Empty data provided"</span>));
<a href=#11 id=11 data-nosnippet>11</a>    }
<a href=#12 id=12 data-nosnippet>12</a>
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">let </span>entries: Vec&lt;types::Entry&gt; = bincode::deserialize(bytes)
<a href=#14 id=14 data-nosnippet>14</a>        .map_err(|e| JsValue::from_str(<span class="kw-2">&amp;</span><span class="macro">format!</span>(<span class="string">"Failed to deserialize entries: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#15 id=15 data-nosnippet>15</a>
<a href=#16 id=16 data-nosnippet>16</a>    <span class="prelude-val">Ok</span>(ParsedEntry { slot, entries })
<a href=#17 id=17 data-nosnippet>17</a>}</code></pre></div></section></main></body></html>