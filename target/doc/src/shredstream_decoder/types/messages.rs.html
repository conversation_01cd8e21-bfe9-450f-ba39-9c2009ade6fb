<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src/types/messages.rs`."><title>messages.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="shredstream_decoder" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../../static.files/storage-82c7156e.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">shredstream_decoder/types/</div>messages.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>solana_hash::Hash;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>solana_short_vec <span class="kw">as </span>short_vec;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>tsify::Tsify;
<a href=#5 id=5 data-nosnippet>5</a>
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span><span class="kw">super</span>::accounts::{MessageAddressTableLookup, Pubkey};
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span><span class="kw">super</span>::instructions::CompiledInstruction;
<a href=#8 id=8 data-nosnippet>8</a>
<a href=#9 id=9 data-nosnippet>9</a><span class="attr">#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Tsify)]
<a href=#10 id=10 data-nosnippet>10</a>#[tsify(into_wasm_abi, from_wasm_abi)]
<a href=#11 id=11 data-nosnippet>11</a></span><span class="kw">pub enum </span>VersionedMessage {
<a href=#12 id=12 data-nosnippet>12</a>    Legacy(LegacyMessage),
<a href=#13 id=13 data-nosnippet>13</a>    V0(V0Message),
<a href=#14 id=14 data-nosnippet>14</a>}
<a href=#15 id=15 data-nosnippet>15</a>
<a href=#16 id=16 data-nosnippet>16</a><span class="kw">impl </span>Default <span class="kw">for </span>VersionedMessage {
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#18 id=18 data-nosnippet>18</a>        <span class="self">Self</span>::Legacy(LegacyMessage::default())
<a href=#19 id=19 data-nosnippet>19</a>    }
<a href=#20 id=20 data-nosnippet>20</a>}
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a><span class="attr">#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default, Tsify)]
<a href=#23 id=23 data-nosnippet>23</a>#[tsify(into_wasm_abi, from_wasm_abi)]
<a href=#24 id=24 data-nosnippet>24</a></span><span class="kw">pub struct </span>LegacyMessage {
<a href=#25 id=25 data-nosnippet>25</a>    <span class="kw">pub </span>header: MessageHeader,
<a href=#26 id=26 data-nosnippet>26</a>    <span class="attr">#[serde(with = <span class="string">"short_vec"</span>)]
<a href=#27 id=27 data-nosnippet>27</a>    </span><span class="kw">pub </span>account_keys: Vec&lt;Pubkey&gt;,
<a href=#28 id=28 data-nosnippet>28</a>    <span class="kw">pub </span>recent_blockhash: Hash,
<a href=#29 id=29 data-nosnippet>29</a>    <span class="attr">#[serde(with = <span class="string">"short_vec"</span>)]
<a href=#30 id=30 data-nosnippet>30</a>    </span><span class="kw">pub </span>instructions: Vec&lt;CompiledInstruction&gt;,
<a href=#31 id=31 data-nosnippet>31</a>}
<a href=#32 id=32 data-nosnippet>32</a>
<a href=#33 id=33 data-nosnippet>33</a><span class="attr">#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default, Tsify)]
<a href=#34 id=34 data-nosnippet>34</a>#[tsify(into_wasm_abi, from_wasm_abi)]
<a href=#35 id=35 data-nosnippet>35</a></span><span class="kw">pub struct </span>V0Message {
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>header: MessageHeader,
<a href=#37 id=37 data-nosnippet>37</a>    <span class="attr">#[serde(with = <span class="string">"short_vec"</span>)]
<a href=#38 id=38 data-nosnippet>38</a>    </span><span class="kw">pub </span>account_keys: Vec&lt;Pubkey&gt;,
<a href=#39 id=39 data-nosnippet>39</a>    <span class="kw">pub </span>recent_blockhash: Hash,
<a href=#40 id=40 data-nosnippet>40</a>    <span class="attr">#[serde(with = <span class="string">"short_vec"</span>)]
<a href=#41 id=41 data-nosnippet>41</a>    </span><span class="kw">pub </span>instructions: Vec&lt;CompiledInstruction&gt;,
<a href=#42 id=42 data-nosnippet>42</a>    <span class="attr">#[serde(with = <span class="string">"short_vec"</span>)]
<a href=#43 id=43 data-nosnippet>43</a>    </span><span class="kw">pub </span>address_table_lookups: Vec&lt;MessageAddressTableLookup&gt;,
<a href=#44 id=44 data-nosnippet>44</a>}
<a href=#45 id=45 data-nosnippet>45</a>
<a href=#46 id=46 data-nosnippet>46</a><span class="attr">#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default, Tsify)]
<a href=#47 id=47 data-nosnippet>47</a>#[tsify(into_wasm_abi, from_wasm_abi)]
<a href=#48 id=48 data-nosnippet>48</a></span><span class="kw">pub struct </span>MessageHeader {
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub </span>num_required_signatures: u8,
<a href=#50 id=50 data-nosnippet>50</a>    <span class="kw">pub </span>num_readonly_signed_accounts: u8,
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub </span>num_readonly_unsigned_accounts: u8,
<a href=#52 id=52 data-nosnippet>52</a>}</code></pre></div></section></main></body></html>