<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src/types/accounts.rs`."><title>accounts.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="shredstream_decoder" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../../static.files/storage-82c7156e.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">shredstream_decoder/types/</div>accounts.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>solana_short_vec <span class="kw">as </span>short_vec;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>tsify::Tsify;
<a href=#4 id=4 data-nosnippet>4</a>
<a href=#5 id=5 data-nosnippet>5</a><span class="attr">#[derive(
<a href=#6 id=6 data-nosnippet>6</a>    Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Copy, Default, PartialOrd, Ord, Hash, Tsify,
<a href=#7 id=7 data-nosnippet>7</a>)]
<a href=#8 id=8 data-nosnippet>8</a>#[tsify(into_wasm_abi, from_wasm_abi)]
<a href=#9 id=9 data-nosnippet>9</a>#[repr(transparent)]
<a href=#10 id=10 data-nosnippet>10</a></span><span class="kw">pub struct </span>Pubkey([u8; <span class="number">32</span>]);
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">impl </span>Pubkey {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub const fn </span>new_from_array(pubkey: [u8; <span class="number">32</span>]) -&gt; <span class="self">Self </span>{
<a href=#14 id=14 data-nosnippet>14</a>        <span class="self">Self</span>(pubkey)
<a href=#15 id=15 data-nosnippet>15</a>    }
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub fn </span>to_bytes(<span class="self">self</span>) -&gt; [u8; <span class="number">32</span>] {
<a href=#18 id=18 data-nosnippet>18</a>        <span class="self">self</span>.<span class="number">0
<a href=#19 id=19 data-nosnippet>19</a>    </span>}
<a href=#20 id=20 data-nosnippet>20</a>}
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a><span class="kw">impl </span>AsRef&lt;[u8]&gt; <span class="kw">for </span>Pubkey {
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">fn </span>as_ref(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>[u8] {
<a href=#24 id=24 data-nosnippet>24</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.<span class="number">0</span>[..]
<a href=#25 id=25 data-nosnippet>25</a>    }
<a href=#26 id=26 data-nosnippet>26</a>}
<a href=#27 id=27 data-nosnippet>27</a>
<a href=#28 id=28 data-nosnippet>28</a><span class="kw">impl </span>From&lt;[u8; <span class="number">32</span>]&gt; <span class="kw">for </span>Pubkey {
<a href=#29 id=29 data-nosnippet>29</a>    <span class="kw">fn </span>from(from: [u8; <span class="number">32</span>]) -&gt; <span class="self">Self </span>{
<a href=#30 id=30 data-nosnippet>30</a>        <span class="self">Self</span>(from)
<a href=#31 id=31 data-nosnippet>31</a>    }
<a href=#32 id=32 data-nosnippet>32</a>}
<a href=#33 id=33 data-nosnippet>33</a>
<a href=#34 id=34 data-nosnippet>34</a><span class="attr">#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default, Tsify)]
<a href=#35 id=35 data-nosnippet>35</a>#[tsify(into_wasm_abi, from_wasm_abi)]
<a href=#36 id=36 data-nosnippet>36</a></span><span class="kw">pub struct </span>MessageAddressTableLookup {
<a href=#37 id=37 data-nosnippet>37</a>    <span class="kw">pub </span>account_key: Pubkey,
<a href=#38 id=38 data-nosnippet>38</a>    <span class="attr">#[serde(with = <span class="string">"short_vec"</span>)]
<a href=#39 id=39 data-nosnippet>39</a>    </span><span class="kw">pub </span>writable_indexes: Vec&lt;u8&gt;,
<a href=#40 id=40 data-nosnippet>40</a>    <span class="attr">#[serde(with = <span class="string">"short_vec"</span>)]
<a href=#41 id=41 data-nosnippet>41</a>    </span><span class="kw">pub </span>readonly_indexes: Vec&lt;u8&gt;,
<a href=#42 id=42 data-nosnippet>42</a>}</code></pre></div></section></main></body></html>