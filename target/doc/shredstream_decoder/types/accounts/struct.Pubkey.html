<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `Pubkey` struct in crate `shredstream_decoder`."><title>Pubkey in shredstream_decoder::types::accounts - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-916cea96.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="shredstream_decoder" data-themes="" data-resource-suffix="" data-rustdoc-version="1.87.0 (17067e9ac 2025-05-09)" data-channel="1.87.0" data-search-js="search-e7298875.js" data-settings-js="settings-d72f25bb.js" ><script src="../../../static.files/storage-82c7156e.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../../static.files/main-fb8c74a8.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc struct"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../../shredstream_decoder/index.html">shredstream_<wbr>decoder</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Pubkey</a></h2><h3><a href="#implementations">Methods</a></h3><ul class="block method"><li><a href="#method.new_from_array" title="new_from_array">new_from_array</a></li><li><a href="#method.to_bytes" title="to_bytes">to_bytes</a></li></ul><h3><a href="#trait-implementations">Trait Implementations</a></h3><ul class="block trait-implementation"><li><a href="#impl-AsRef%3C%5Bu8%5D%3E-for-Pubkey" title="AsRef&#60;[u8]&#62;">AsRef&#60;[u8]&#62;</a></li><li><a href="#impl-Clone-for-Pubkey" title="Clone">Clone</a></li><li><a href="#impl-Copy-for-Pubkey" title="Copy">Copy</a></li><li><a href="#impl-Debug-for-Pubkey" title="Debug">Debug</a></li><li><a href="#impl-Default-for-Pubkey" title="Default">Default</a></li><li><a href="#impl-Deserialize%3C'de%3E-for-Pubkey" title="Deserialize&#60;&#39;de&#62;">Deserialize&#60;&#39;de&#62;</a></li><li><a href="#impl-Eq-for-Pubkey" title="Eq">Eq</a></li><li><a href="#impl-From%3C%5Bu8;+32%5D%3E-for-Pubkey" title="From&#60;[u8; 32]&#62;">From&#60;[u8; 32]&#62;</a></li><li><a href="#impl-FromWasmAbi-for-Pubkey" title="FromWasmAbi">FromWasmAbi</a></li><li><a href="#impl-Hash-for-Pubkey" title="Hash">Hash</a></li><li><a href="#impl-IntoWasmAbi-for-Pubkey" title="IntoWasmAbi">IntoWasmAbi</a></li><li><a href="#impl-OptionFromWasmAbi-for-Pubkey" title="OptionFromWasmAbi">OptionFromWasmAbi</a></li><li><a href="#impl-OptionIntoWasmAbi-for-Pubkey" title="OptionIntoWasmAbi">OptionIntoWasmAbi</a></li><li><a href="#impl-Ord-for-Pubkey" title="Ord">Ord</a></li><li><a href="#impl-PartialEq-for-Pubkey" title="PartialEq">PartialEq</a></li><li><a href="#impl-PartialOrd-for-Pubkey" title="PartialOrd">PartialOrd</a></li><li><a href="#impl-Serialize-for-Pubkey" title="Serialize">Serialize</a></li><li><a href="#impl-StructuralPartialEq-for-Pubkey" title="StructuralPartialEq">StructuralPartialEq</a></li><li><a href="#impl-Tsify-for-Pubkey" title="Tsify">Tsify</a></li><li><a href="#impl-WasmDescribe-for-Pubkey" title="WasmDescribe">WasmDescribe</a></li></ul><h3><a href="#synthetic-implementations">Auto Trait Implementations</a></h3><ul class="block synthetic-implementation"><li><a href="#impl-Freeze-for-Pubkey" title="Freeze">Freeze</a></li><li><a href="#impl-RefUnwindSafe-for-Pubkey" title="RefUnwindSafe">RefUnwindSafe</a></li><li><a href="#impl-Send-for-Pubkey" title="Send">Send</a></li><li><a href="#impl-Sync-for-Pubkey" title="Sync">Sync</a></li><li><a href="#impl-Unpin-for-Pubkey" title="Unpin">Unpin</a></li><li><a href="#impl-UnwindSafe-for-Pubkey" title="UnwindSafe">UnwindSafe</a></li></ul><h3><a href="#blanket-implementations">Blanket Implementations</a></h3><ul class="block blanket-implementation"><li><a href="#impl-Any-for-T" title="Any">Any</a></li><li><a href="#impl-Borrow%3CT%3E-for-T" title="Borrow&#60;T&#62;">Borrow&#60;T&#62;</a></li><li><a href="#impl-BorrowMut%3CT%3E-for-T" title="BorrowMut&#60;T&#62;">BorrowMut&#60;T&#62;</a></li><li><a href="#impl-CloneToUninit-for-T" title="CloneToUninit">CloneToUninit</a></li><li><a href="#impl-DeserializeOwned-for-T" title="DeserializeOwned">DeserializeOwned</a></li><li><a href="#impl-From%3CT%3E-for-T" title="From&#60;T&#62;">From&#60;T&#62;</a></li><li><a href="#impl-Into%3CU%3E-for-T" title="Into&#60;U&#62;">Into&#60;U&#62;</a></li><li><a href="#impl-ReturnWasmAbi-for-T" title="ReturnWasmAbi">ReturnWasmAbi</a></li><li><a href="#impl-ToOwned-for-T" title="ToOwned">ToOwned</a></li><li><a href="#impl-TryFrom%3CU%3E-for-T" title="TryFrom&#60;U&#62;">TryFrom&#60;U&#62;</a></li><li><a href="#impl-TryInto%3CU%3E-for-T" title="TryInto&#60;U&#62;">TryInto&#60;U&#62;</a></li></ul></section><div id="rustdoc-modnav"><h2><a href="index.html">In shredstream_<wbr>decoder::<wbr>types::<wbr>accounts</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../../index.html">shredstream_decoder</a>::<wbr><a href="../index.html">types</a>::<wbr><a href="index.html">accounts</a></div><h1>Struct <span class="struct">Pubkey</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../../src/shredstream_decoder/types/accounts.rs.html#10">Source</a> </span></div><pre class="rust item-decl"><code>pub struct Pubkey(<span class="comment">/* private fields */</span>);</code></pre><h2 id="implementations" class="section-header">Implementations<a href="#implementations" class="anchor">§</a></h2><div id="implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#12-20">Source</a><a href="#impl-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><section id="method.new_from_array" class="method"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#13-15">Source</a><h4 class="code-header">pub const fn <a href="#method.new_from_array" class="fn">new_from_array</a>(pubkey: [<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.array.html">32</a>]) -&gt; Self</h4></section><section id="method.to_bytes" class="method"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#17-19">Source</a><h4 class="code-header">pub fn <a href="#method.to_bytes" class="fn">to_bytes</a>(self) -&gt; [<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.array.html">32</a>]</h4></section></div></details></div><h2 id="trait-implementations" class="section-header">Trait Implementations<a href="#trait-implementations" class="anchor">§</a></h2><div id="trait-implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-AsRef%3C%5Bu8%5D%3E-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#22-26">Source</a><a href="#impl-AsRef%3C%5Bu8%5D%3E-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.AsRef.html" title="trait core::convert::AsRef">AsRef</a>&lt;[<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>]&gt; for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.as_ref" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#23-25">Source</a><a href="#method.as_ref" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.AsRef.html#tymethod.as_ref" class="fn">as_ref</a>(&amp;self) -&gt; &amp;[<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>] <a href="#" class="tooltip" data-notable-ty="&amp;[u8]">ⓘ</a></h4></section></summary><div class='docblock'>Converts this type into a shared reference of the (usually inferred) input type.</div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Clone-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Clone-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.clone" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.clone" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html#tymethod.clone" class="fn">clone</a>(&amp;self) -&gt; <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h4></section></summary><div class='docblock'>Returns a copy of the value. <a href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html#tymethod.clone">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.clone_from" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/clone.rs.html#174">Source</a></span><a href="#method.clone_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html#method.clone_from" class="fn">clone_from</a>(&amp;mut self, source: &amp;Self)</h4></section></summary><div class='docblock'>Performs copy-assignment from <code>source</code>. <a href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html#method.clone_from">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Debug-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Debug-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/fmt/trait.Debug.html" title="trait core::fmt::Debug">Debug</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.fmt" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.fmt" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/fmt/trait.Debug.html#tymethod.fmt" class="fn">fmt</a>(&amp;self, f: &amp;mut <a class="struct" href="https://doc.rust-lang.org/1.87.0/core/fmt/struct.Formatter.html" title="struct core::fmt::Formatter">Formatter</a>&lt;'_&gt;) -&gt; <a class="type" href="https://doc.rust-lang.org/1.87.0/core/fmt/type.Result.html" title="type core::fmt::Result">Result</a></h4></section></summary><div class='docblock'>Formats the value using the given formatter. <a href="https://doc.rust-lang.org/1.87.0/core/fmt/trait.Debug.html#tymethod.fmt">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Default-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Default-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/default/trait.Default.html" title="trait core::default::Default">Default</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.default" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.default" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/default/trait.Default.html#tymethod.default" class="fn">default</a>() -&gt; <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h4></section></summary><div class='docblock'>Returns the “default value” for a type. <a href="https://doc.rust-lang.org/1.87.0/core/default/trait.Default.html#tymethod.default">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Deserialize%3C'de%3E-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Deserialize%3C'de%3E-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl&lt;'de&gt; <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html" title="trait serde::de::Deserialize">Deserialize</a>&lt;'de&gt; for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.deserialize" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.deserialize" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html#tymethod.deserialize" class="fn">deserialize</a>&lt;__D&gt;(__deserializer: __D) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, __D::<a class="associatedtype" href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html#associatedtype.Error" title="type serde::de::Deserializer::Error">Error</a>&gt;<div class="where">where
    __D: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html" title="trait serde::de::Deserializer">Deserializer</a>&lt;'de&gt;,</div></h4></section></summary><div class='docblock'>Deserialize this value from the given Serde deserializer. <a href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html#tymethod.deserialize">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-From%3C%5Bu8;+32%5D%3E-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#28-32">Source</a><a href="#impl-From%3C%5Bu8;+32%5D%3E-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;[<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.array.html">32</a>]&gt; for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#29-31">Source</a><a href="#method.from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(from: [<a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.array.html">32</a>]) -&gt; Self</h4></section></summary><div class='docblock'>Converts to this type from the input type.</div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-FromWasmAbi-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-FromWasmAbi-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.FromWasmAbi.html" title="trait wasm_bindgen::convert::traits::FromWasmAbi">FromWasmAbi</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a><div class="where">where
    Self: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.DeserializeOwned.html" title="trait serde::de::DeserializeOwned">DeserializeOwned</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Abi-1" class="associatedtype trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#associatedtype.Abi-1" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.FromWasmAbi.html#associatedtype.Abi" class="associatedtype">Abi</a> = &lt;JsType as <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.FromWasmAbi.html" title="trait wasm_bindgen::convert::traits::FromWasmAbi">FromWasmAbi</a>&gt;::<a class="associatedtype" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.FromWasmAbi.html#associatedtype.Abi" title="type wasm_bindgen::convert::traits::FromWasmAbi::Abi">Abi</a></h4></section></summary><div class='docblock'>The Wasm ABI type that this converts from when coming back out from the
ABI boundary.</div></details><details class="toggle method-toggle" open><summary><section id="method.from_abi" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.from_abi" class="anchor">§</a><h4 class="code-header">unsafe fn <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.FromWasmAbi.html#tymethod.from_abi" class="fn">from_abi</a>(js: Self::<a class="associatedtype" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.FromWasmAbi.html#associatedtype.Abi" title="type wasm_bindgen::convert::traits::FromWasmAbi::Abi">Abi</a>) -&gt; Self</h4></section></summary><div class='docblock'>Recover a <code>Self</code> from <code>Self::Abi</code>. <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.FromWasmAbi.html#tymethod.from_abi">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Hash-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Hash-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hash.html" title="trait core::hash::Hash">Hash</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.hash" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.hash" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hash.html#tymethod.hash" class="fn">hash</a>&lt;__H: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hasher.html" title="trait core::hash::Hasher">Hasher</a>&gt;(&amp;self, state: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;mut __H</a>)</h4></section></summary><div class='docblock'>Feeds this value into the given <a href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hasher.html" title="trait core::hash::Hasher"><code>Hasher</code></a>. <a href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hash.html#tymethod.hash">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.hash_slice" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.3.0">1.3.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/hash/mod.rs.html#235-237">Source</a></span><a href="#method.hash_slice" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hash.html#method.hash_slice" class="fn">hash_slice</a>&lt;H&gt;(data: &amp;[Self], state: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;mut H</a>)<div class="where">where
    H: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hasher.html" title="trait core::hash::Hasher">Hasher</a>,
    Self: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h4></section></summary><div class='docblock'>Feeds a slice of this type into the given <a href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hasher.html" title="trait core::hash::Hasher"><code>Hasher</code></a>. <a href="https://doc.rust-lang.org/1.87.0/core/hash/trait.Hash.html#method.hash_slice">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-IntoWasmAbi-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-IntoWasmAbi-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html" title="trait wasm_bindgen::convert::traits::IntoWasmAbi">IntoWasmAbi</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a><div class="where">where
    Self: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html" title="trait serde::ser::Serialize">Serialize</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Abi" class="associatedtype trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#associatedtype.Abi" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html#associatedtype.Abi" class="associatedtype">Abi</a> = &lt;JsType as <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html" title="trait wasm_bindgen::convert::traits::IntoWasmAbi">IntoWasmAbi</a>&gt;::<a class="associatedtype" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html#associatedtype.Abi" title="type wasm_bindgen::convert::traits::IntoWasmAbi::Abi">Abi</a></h4></section></summary><div class='docblock'>The Wasm ABI type that this converts into when crossing the ABI
boundary.</div></details><details class="toggle method-toggle" open><summary><section id="method.into_abi" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.into_abi" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html#tymethod.into_abi" class="fn">into_abi</a>(self) -&gt; Self::<a class="associatedtype" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html#associatedtype.Abi" title="type wasm_bindgen::convert::traits::IntoWasmAbi::Abi">Abi</a></h4></section></summary><div class='docblock'>Convert <code>self</code> into <code>Self::Abi</code> so that it can be sent across the wasm
ABI boundary.</div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-OptionFromWasmAbi-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-OptionFromWasmAbi-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.OptionFromWasmAbi.html" title="trait wasm_bindgen::convert::traits::OptionFromWasmAbi">OptionFromWasmAbi</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a><div class="where">where
    Self: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.DeserializeOwned.html" title="trait serde::de::DeserializeOwned">DeserializeOwned</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.is_none" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.is_none" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.OptionFromWasmAbi.html#tymethod.is_none" class="fn">is_none</a>(js: &amp;Self::<a class="associatedtype" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.FromWasmAbi.html#associatedtype.Abi" title="type wasm_bindgen::convert::traits::FromWasmAbi::Abi">Abi</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests whether the argument is a “none” instance. If so it will be
deserialized as <code>None</code>, and otherwise it will be passed to
<code>FromWasmAbi</code>.</div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-OptionIntoWasmAbi-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-OptionIntoWasmAbi-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.OptionIntoWasmAbi.html" title="trait wasm_bindgen::convert::traits::OptionIntoWasmAbi">OptionIntoWasmAbi</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a><div class="where">where
    Self: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html" title="trait serde::ser::Serialize">Serialize</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.none" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.none" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.OptionIntoWasmAbi.html#tymethod.none" class="fn">none</a>() -&gt; Self::<a class="associatedtype" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html#associatedtype.Abi" title="type wasm_bindgen::convert::traits::IntoWasmAbi::Abi">Abi</a></h4></section></summary><div class='docblock'>Returns an ABI instance indicating “none”, which JS will interpret as
the <code>None</code> branch of this option. <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.OptionIntoWasmAbi.html#tymethod.none">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Ord-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Ord-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html" title="trait core::cmp::Ord">Ord</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.cmp" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.cmp" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html#tymethod.cmp" class="fn">cmp</a>(&amp;self, other: &amp;<a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/cmp/enum.Ordering.html" title="enum core::cmp::Ordering">Ordering</a></h4></section></summary><div class='docblock'>This method returns an <a href="https://doc.rust-lang.org/1.87.0/core/cmp/enum.Ordering.html" title="enum core::cmp::Ordering"><code>Ordering</code></a> between <code>self</code> and <code>other</code>. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html#tymethod.cmp">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.max" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.21.0">1.21.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/cmp.rs.html#1009-1011">Source</a></span><a href="#method.max" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html#method.max" class="fn">max</a>(self, other: Self) -&gt; Self<div class="where">where
    Self: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h4></section></summary><div class='docblock'>Compares and returns the maximum of two values. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html#method.max">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.min" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.21.0">1.21.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/cmp.rs.html#1048-1050">Source</a></span><a href="#method.min" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html#method.min" class="fn">min</a>(self, other: Self) -&gt; Self<div class="where">where
    Self: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h4></section></summary><div class='docblock'>Compares and returns the minimum of two values. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html#method.min">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.clamp" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.50.0">1.50.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/cmp.rs.html#1074-1076">Source</a></span><a href="#method.clamp" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html#method.clamp" class="fn">clamp</a>(self, min: Self, max: Self) -&gt; Self<div class="where">where
    Self: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h4></section></summary><div class='docblock'>Restrict a value to a certain interval. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Ord.html#method.clamp">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-PartialEq-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-PartialEq-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialEq.html" title="trait core::cmp::PartialEq">PartialEq</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.eq" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.eq" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialEq.html#tymethod.eq" class="fn">eq</a>(&amp;self, other: &amp;<a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class="toggle method-toggle" open><summary><section id="method.ne" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/cmp.rs.html#262">Source</a></span><a href="#method.ne" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialEq.html#method.ne" class="fn">ne</a>(&amp;self, other: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;Rhs</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,
and should not be overridden without very good reason.</div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-PartialOrd-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-PartialOrd-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html" title="trait core::cmp::PartialOrd">PartialOrd</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.partial_cmp" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.partial_cmp" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#tymethod.partial_cmp" class="fn">partial_cmp</a>(&amp;self, other: &amp;<a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="enum" href="https://doc.rust-lang.org/1.87.0/core/cmp/enum.Ordering.html" title="enum core::cmp::Ordering">Ordering</a>&gt;</h4></section></summary><div class='docblock'>This method returns an ordering between <code>self</code> and <code>other</code> values if one exists. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#tymethod.partial_cmp">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.lt" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/cmp.rs.html#1382">Source</a></span><a href="#method.lt" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#method.lt" class="fn">lt</a>(&amp;self, other: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;Rhs</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests less than (for <code>self</code> and <code>other</code>) and is used by the <code>&lt;</code> operator. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#method.lt">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.le" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/cmp.rs.html#1400">Source</a></span><a href="#method.le" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#method.le" class="fn">le</a>(&amp;self, other: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;Rhs</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests less than or equal to (for <code>self</code> and <code>other</code>) and is used by the
<code>&lt;=</code> operator. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#method.le">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.gt" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/cmp.rs.html#1418">Source</a></span><a href="#method.gt" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#method.gt" class="fn">gt</a>(&amp;self, other: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;Rhs</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests greater than (for <code>self</code> and <code>other</code>) and is used by the <code>&gt;</code>
operator. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#method.gt">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.ge" class="method trait-impl"><span class="rightside"><span class="since" title="Stable since Rust version 1.0.0">1.0.0</span> · <a class="src" href="https://doc.rust-lang.org/1.87.0/src/core/cmp.rs.html#1436">Source</a></span><a href="#method.ge" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#method.ge" class="fn">ge</a>(&amp;self, other: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;Rhs</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.bool.html">bool</a></h4></section></summary><div class='docblock'>Tests greater than or equal to (for <code>self</code> and <code>other</code>) and is used by
the <code>&gt;=</code> operator. <a href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.PartialOrd.html#method.ge">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Serialize-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Serialize-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html" title="trait serde::ser::Serialize">Serialize</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.serialize" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.serialize" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html#tymethod.serialize" class="fn">serialize</a>&lt;__S&gt;(&amp;self, __serializer: __S) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;__S::<a class="associatedtype" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Ok" title="type serde::ser::Serializer::Ok">Ok</a>, __S::<a class="associatedtype" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Error" title="type serde::ser::Serializer::Error">Error</a>&gt;<div class="where">where
    __S: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html" title="trait serde::ser::Serializer">Serializer</a>,</div></h4></section></summary><div class='docblock'>Serialize this value into the given Serde serializer. <a href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html#tymethod.serialize">Read more</a></div></details></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Tsify-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Tsify-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl Tsify for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><section id="associatedconstant.DECL" class="associatedconstant trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#associatedconstant.DECL" class="anchor">§</a><h4 class="code-header">const <a class="constant">DECL</a>: &amp;'static <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.str.html">str</a> = &quot;export type Pubkey = number[];&quot;</h4></section><section id="associatedtype.JsType" class="associatedtype trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#associatedtype.JsType" class="anchor">§</a><h4 class="code-header">type <a class="associatedtype">JsType</a> = JsType</h4></section><section id="method.into_js" class="method trait-impl"><a href="#method.into_js" class="anchor">§</a><h4 class="code-header">fn <a class="fn">into_js</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self::JsType, <a class="struct" href="https://docs.rs/serde_json/1.0.140/serde_json/error/struct.Error.html" title="struct serde_json::error::Error">Error</a>&gt;<div class="where">where
    Self: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html" title="trait serde::ser::Serialize">Serialize</a>,</div></h4></section><section id="method.from_js" class="method trait-impl"><a href="#method.from_js" class="anchor">§</a><h4 class="code-header">fn <a class="fn">from_js</a>&lt;T&gt;(js: T) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, <a class="struct" href="https://docs.rs/serde_json/1.0.140/serde_json/error/struct.Error.html" title="struct serde_json::error::Error">Error</a>&gt;<div class="where">where
    T: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;<a class="struct" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/struct.JsValue.html" title="struct wasm_bindgen::JsValue">JsValue</a>&gt;,
    Self: <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.DeserializeOwned.html" title="trait serde::de::DeserializeOwned">DeserializeOwned</a>,</div></h4></section></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-WasmDescribe-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-WasmDescribe-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl WasmDescribe for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></summary><div class="impl-items"><section id="method.describe" class="method trait-impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#method.describe" class="anchor">§</a><h4 class="code-header">fn <a href="#tymethod.describe" class="fn">describe</a>()</h4></section></div></details><section id="impl-Copy-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Copy-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Copy.html" title="trait core::marker::Copy">Copy</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section><section id="impl-Eq-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-Eq-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/cmp/trait.Eq.html" title="trait core::cmp::Eq">Eq</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section><section id="impl-StructuralPartialEq-for-Pubkey" class="impl"><a class="src rightside" href="../../../src/shredstream_decoder/types/accounts.rs.html#6">Source</a><a href="#impl-StructuralPartialEq-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.StructuralPartialEq.html" title="trait core::marker::StructuralPartialEq">StructuralPartialEq</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></div><h2 id="synthetic-implementations" class="section-header">Auto Trait Implementations<a href="#synthetic-implementations" class="anchor">§</a></h2><div id="synthetic-implementations-list"><section id="impl-Freeze-for-Pubkey" class="impl"><a href="#impl-Freeze-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Freeze.html" title="trait core::marker::Freeze">Freeze</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section><section id="impl-RefUnwindSafe-for-Pubkey" class="impl"><a href="#impl-RefUnwindSafe-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/panic/unwind_safe/trait.RefUnwindSafe.html" title="trait core::panic::unwind_safe::RefUnwindSafe">RefUnwindSafe</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section><section id="impl-Send-for-Pubkey" class="impl"><a href="#impl-Send-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section><section id="impl-Sync-for-Pubkey" class="impl"><a href="#impl-Sync-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section><section id="impl-Unpin-for-Pubkey" class="impl"><a href="#impl-Unpin-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Unpin.html" title="trait core::marker::Unpin">Unpin</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section><section id="impl-UnwindSafe-for-Pubkey" class="impl"><a href="#impl-UnwindSafe-for-Pubkey" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/panic/unwind_safe/trait.UnwindSafe.html" title="trait core::panic::unwind_safe::UnwindSafe">UnwindSafe</a> for <a class="struct" href="struct.Pubkey.html" title="struct shredstream_decoder::types::accounts::Pubkey">Pubkey</a></h3></section></div><h2 id="blanket-implementations" class="section-header">Blanket Implementations<a href="#blanket-implementations" class="anchor">§</a></h2><div id="blanket-implementations-list"><details class="toggle implementors-toggle"><summary><section id="impl-Any-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/any.rs.html#138">Source</a><a href="#impl-Any-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/any/trait.Any.html" title="trait core::any::Any">Any</a> for T<div class="where">where
    T: 'static + ?<a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.type_id" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/any.rs.html#139">Source</a><a href="#method.type_id" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/any/trait.Any.html#tymethod.type_id" class="fn">type_id</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.87.0/core/any/struct.TypeId.html" title="struct core::any::TypeId">TypeId</a></h4></section></summary><div class='docblock'>Gets the <code>TypeId</code> of <code>self</code>. <a href="https://doc.rust-lang.org/1.87.0/core/any/trait.Any.html#tymethod.type_id">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Borrow%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/borrow.rs.html#209">Source</a><a href="#impl-Borrow%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.Borrow.html" title="trait core::borrow::Borrow">Borrow</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/borrow.rs.html#211">Source</a><a href="#method.borrow" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.Borrow.html#tymethod.borrow" class="fn">borrow</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;T</a></h4></section></summary><div class='docblock'>Immutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.Borrow.html#tymethod.borrow">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-BorrowMut%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/borrow.rs.html#217">Source</a><a href="#impl-BorrowMut%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.BorrowMut.html" title="trait core::borrow::BorrowMut">BorrowMut</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.87.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow_mut" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/borrow.rs.html#218">Source</a><a href="#method.borrow_mut" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut" class="fn">borrow_mut</a>(&amp;mut self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;mut T</a></h4></section></summary><div class='docblock'>Mutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.87.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-CloneToUninit-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/clone.rs.html#441">Source</a><a href="#impl-CloneToUninit-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/clone/trait.CloneToUninit.html" title="trait core::clone::CloneToUninit">CloneToUninit</a> for T<div class="where">where
    T: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.clone_to_uninit" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/clone.rs.html#443">Source</a><a href="#method.clone_to_uninit" class="anchor">§</a><h4 class="code-header">unsafe fn <a href="https://doc.rust-lang.org/1.87.0/core/clone/trait.CloneToUninit.html#tymethod.clone_to_uninit" class="fn">clone_to_uninit</a>(&amp;self, dest: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.pointer.html">*mut </a><a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.u8.html">u8</a>)</h4></section></summary><span class="item-info"><div class="stab unstable"><span class="emoji">🔬</span><span>This is a nightly-only experimental API. (<code>clone_to_uninit</code>)</span></div></span><div class='docblock'>Performs copy-assignment from <code>self</code> to <code>dest</code>. <a href="https://doc.rust-lang.org/1.87.0/core/clone/trait.CloneToUninit.html#tymethod.clone_to_uninit">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-From%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#767">Source</a><a href="#impl-From%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from-1" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#770">Source</a><a href="#method.from-1" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(t: T) -&gt; T</h4></section></summary><div class="docblock"><p>Returns the argument unchanged.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Into%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#750-752">Source</a><a href="#impl-Into%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#760">Source</a><a href="#method.into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html#tymethod.into" class="fn">into</a>(self) -&gt; U</h4></section></summary><div class="docblock"><p>Calls <code>U::from(self)</code>.</p>
<p>That is, this conversion is whatever the implementation of
<code><a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for U</code> chooses to do.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-ReturnWasmAbi-for-T" class="impl"><a class="src rightside" href="https://docs.rs/wasm-bindgen/0.2/src/wasm_bindgen/convert/traits.rs.html#246">Source</a><a href="#impl-ReturnWasmAbi-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.ReturnWasmAbi.html" title="trait wasm_bindgen::convert::traits::ReturnWasmAbi">ReturnWasmAbi</a> for T<div class="where">where
    T: <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html" title="trait wasm_bindgen::convert::traits::IntoWasmAbi">IntoWasmAbi</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Abi-2" class="associatedtype trait-impl"><a class="src rightside" href="https://docs.rs/wasm-bindgen/0.2/src/wasm_bindgen/convert/traits.rs.html#247">Source</a><a href="#associatedtype.Abi-2" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.ReturnWasmAbi.html#associatedtype.Abi" class="associatedtype">Abi</a> = &lt;T as <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html" title="trait wasm_bindgen::convert::traits::IntoWasmAbi">IntoWasmAbi</a>&gt;::<a class="associatedtype" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.IntoWasmAbi.html#associatedtype.Abi" title="type wasm_bindgen::convert::traits::IntoWasmAbi::Abi">Abi</a></h4></section></summary><div class='docblock'>Same as <code>IntoWasmAbi::Abi</code></div></details><details class="toggle method-toggle" open><summary><section id="method.return_abi" class="method trait-impl"><a class="src rightside" href="https://docs.rs/wasm-bindgen/0.2/src/wasm_bindgen/convert/traits.rs.html#250">Source</a><a href="#method.return_abi" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.ReturnWasmAbi.html#tymethod.return_abi" class="fn">return_abi</a>(self) -&gt; &lt;T as <a class="trait" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.ReturnWasmAbi.html" title="trait wasm_bindgen::convert::traits::ReturnWasmAbi">ReturnWasmAbi</a>&gt;::<a class="associatedtype" href="https://docs.rs/wasm-bindgen/0.2/wasm_bindgen/convert/traits/trait.ReturnWasmAbi.html#associatedtype.Abi" title="type wasm_bindgen::convert::traits::ReturnWasmAbi::Abi">Abi</a></h4></section></summary><div class='docblock'>Same as <code>IntoWasmAbi::into_abi</code>, except that it may throw and never
return in the case of <code>Err</code>.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-ToOwned-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/alloc/borrow.rs.html#82-84">Source</a><a href="#impl-ToOwned-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/alloc/borrow/trait.ToOwned.html" title="trait alloc::borrow::ToOwned">ToOwned</a> for T<div class="where">where
    T: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/clone/trait.Clone.html" title="trait core::clone::Clone">Clone</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Owned" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/alloc/borrow.rs.html#86">Source</a><a href="#associatedtype.Owned" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.87.0/alloc/borrow/trait.ToOwned.html#associatedtype.Owned" class="associatedtype">Owned</a> = T</h4></section></summary><div class='docblock'>The resulting type after obtaining ownership.</div></details><details class="toggle method-toggle" open><summary><section id="method.to_owned" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/alloc/borrow.rs.html#87">Source</a><a href="#method.to_owned" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/alloc/borrow/trait.ToOwned.html#tymethod.to_owned" class="fn">to_owned</a>(&amp;self) -&gt; T</h4></section></summary><div class='docblock'>Creates owned data from borrowed data, usually by cloning. <a href="https://doc.rust-lang.org/1.87.0/alloc/borrow/trait.ToOwned.html#tymethod.to_owned">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.clone_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/alloc/borrow.rs.html#91">Source</a><a href="#method.clone_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/alloc/borrow/trait.ToOwned.html#method.clone_into" class="fn">clone_into</a>(&amp;self, target: <a class="primitive" href="https://doc.rust-lang.org/1.87.0/std/primitive.reference.html">&amp;mut T</a>)</h4></section></summary><div class='docblock'>Uses borrowed data to replace owned data, usually by cloning. <a href="https://doc.rust-lang.org/1.87.0/alloc/borrow/trait.ToOwned.html#method.clone_into">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryFrom%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#806-808">Source</a><a href="#impl-TryFrom%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error-1" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#810">Source</a><a href="#associatedtype.Error-1" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#associatedtype.Error" class="associatedtype">Error</a> = <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/convert/enum.Infallible.html" title="enum core::convert::Infallible">Infallible</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#813">Source</a><a href="#method.try_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#tymethod.try_from" class="fn">try_from</a>(value: U) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, &lt;T as <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryInto%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#791-793">Source</a><a href="#impl-TryInto%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryInto.html" title="trait core::convert::TryInto">TryInto</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#795">Source</a><a href="#associatedtype.Error" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryInto.html#associatedtype.Error" class="associatedtype">Error</a> = &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.87.0/src/core/convert/mod.rs.html#798">Source</a><a href="#method.try_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryInto.html#tymethod.try_into" class="fn">try_into</a>(self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.87.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;U, &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.87.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><section id="impl-DeserializeOwned-for-T" class="impl"><a class="src rightside" href="https://docs.rs/serde/1.0.219/src/serde/de/mod.rs.html#614">Source</a><a href="#impl-DeserializeOwned-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.DeserializeOwned.html" title="trait serde::de::DeserializeOwned">DeserializeOwned</a> for T<div class="where">where
    T: for&lt;'de&gt; <a class="trait" href="https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html" title="trait serde::de::Deserialize">Deserialize</a>&lt;'de&gt;,</div></h3></section></div><script type="text/json" id="notable-traits-data">{"&[u8]":"<h3>Notable traits for <code>&amp;[<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.87.0/std/primitive.u8.html\">u8</a>]</code></h3><pre><code><div class=\"where\">impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.87.0/std/io/trait.Read.html\" title=\"trait std::io::Read\">Read</a> for &amp;[<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.87.0/std/primitive.u8.html\">u8</a>]</div>"}</script></section></div></main></body></html>