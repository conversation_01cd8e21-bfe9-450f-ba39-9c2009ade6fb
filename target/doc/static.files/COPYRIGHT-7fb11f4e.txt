# REUSE-IgnoreStart

These documentation pages include resources by third parties. This copyright
file applies only to those resources. The following third party resources are
included, and carry their own copyright notices and license terms:

* Fira Sans (FiraSans-Regular.woff2, FiraSans-Medium.woff2):

    Copyright (c) 2014, Mozilla Foundation https://mozilla.org/
    with Reserved Font Name Fira Sans.

    Copyright (c) 2014, Telefonica S.A.

    Licensed under the SIL Open Font License, Version 1.1.
    See FiraSans-LICENSE.txt.

* rustdoc.css, main.js, and playpen.js:

    Copyright 2015 The Rust Developers.
    Licensed under the Apache License, Version 2.0 (see LICENSE-APACHE.txt) or
    the MIT license (LICENSE-MIT.txt) at your option.

* normalize.css:

    Copyright (c) <PERSON> and <PERSON>.
    Licensed under the MIT license (see LICENSE-MIT.txt).

* Source Code Pro (SourceCodePro-Regular.ttf.woff2,
    SourceCodePro-Semibold.ttf.woff2, SourceCodePro-It.ttf.woff2):

    Copyright 2010, 2012 Adobe Systems Incorporated (http://www.adobe.com/),
    with Reserved Font Name 'Source'. All Rights Reserved. Source is a trademark
    of Adobe Systems Incorporated in the United States and/or other countries.

    Licensed under the SIL Open Font License, Version 1.1.
    See SourceCodePro-LICENSE.txt.

* Source Serif 4 (SourceSerif4-Regular.ttf.woff2, SourceSerif4-Bold.ttf.woff2,
    SourceSerif4-It.ttf.woff2, SourceSerif4-Semibold.ttf.woff2):

    Copyright 2014-2021 Adobe (http://www.adobe.com/), with Reserved Font Name
    'Source'. All Rights Reserved. Source is a trademark of Adobe in the United
    States and/or other countries.

    Licensed under the SIL Open Font License, Version 1.1.
    See SourceSerif4-LICENSE.md.

* Nanum Barun Gothic Font (NanumBarunGothic.woff2)

    Copyright 2010, NAVER Corporation (http://www.nhncorp.com)
    with Reserved Font Name Nanum, Naver Nanum, NanumGothic, Naver NanumGothic,
    NanumMyeongjo, Naver NanumMyeongjo, NanumBrush, Naver NanumBrush, NanumPen,
    Naver NanumPen, Naver NanumGothicEco, NanumGothicEco,
    Naver NanumMyeongjoEco, NanumMyeongjoEco, Naver NanumGothicLight,
    NanumGothicLight, NanumBarunGothic, Naver NanumBarunGothic.

    https://hangeul.naver.com/2017/nanum
    https://github.com/hiun/NanumBarunGothic

    Licensed under the SIL Open Font License, Version 1.1.
    See NanumBarunGothic-LICENSE.txt.

* Rust logos (rust-logo.svg, favicon.svg, favicon-32x32.png)

    Copyright 2025 Rust Foundation.
    Licensed under the Creative Commons Attribution license (CC-BY).
    https://rustfoundation.org/policy/rust-trademark-policy/

This copyright file is intended to be distributed with rustdoc output.

# REUSE-IgnoreEnd
