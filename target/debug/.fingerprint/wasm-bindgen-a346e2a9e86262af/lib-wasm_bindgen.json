{"rustc": 15497389221046826682, "features": "[\"default\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 4765055421477619001, "path": 1545970865074875010, "deps": [[3722963349756955755, "once_cell", false, 263002596821618790], [6946689283190175495, "build_script_build", false, 5042520362709327737], [7858942147296547339, "rustversion", false, 2277225609138854432], [9689903380558560274, "serde", false, 2372294934450711281], [10411997081178400487, "cfg_if", false, 18354222509021088450], [11382113702854245495, "wasm_bindgen_macro", false, 6797577357479248608], [15367738274754116744, "serde_json", false, 3292450034025939581]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-a346e2a9e86262af/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}