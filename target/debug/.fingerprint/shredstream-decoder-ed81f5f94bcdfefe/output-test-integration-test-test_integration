{"$message_type":"diagnostic","message":"unused import: `types::ParsedEntry as CustomParsedEntry`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"tests/test_integration.rs","byte_start":175,"byte_end":214,"line_start":7,"line_end":7,"column_start":43,"column_end":82,"is_primary":true,"text":[{"text":"use shredstream_decoder::{decode_entries, types::ParsedEntry as CustomParsedEntry};","highlight_start":43,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"tests/test_integration.rs","byte_start":173,"byte_end":214,"line_start":7,"line_end":7,"column_start":41,"column_end":82,"is_primary":true,"text":[{"text":"use shredstream_decoder::{decode_entries, types::ParsedEntry as CustomParsedEntry};","highlight_start":41,"highlight_end":82}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"tests/test_integration.rs","byte_start":158,"byte_end":159,"line_start":7,"line_end":7,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use shredstream_decoder::{decode_entries, types::ParsedEntry as CustomParsedEntry};","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"tests/test_integration.rs","byte_start":214,"byte_end":215,"line_start":7,"line_end":7,"column_start":82,"column_end":83,"is_primary":true,"text":[{"text":"use shredstream_decoder::{decode_entries, types::ParsedEntry as CustomParsedEntry};","highlight_start":82,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `types::ParsedEntry as CustomParsedEntry`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_integration.rs:7:43\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse shredstream_decoder::{decode_entries, types::ParsedEntry as CustomParsedEntry};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"tests/test_integration.rs","byte_start":1001,"byte_end":1003,"line_start":27,"line_end":27,"column_start":49,"column_end":51,"is_primary":true,"text":[{"text":"                let error_type = classify_error(&e);","highlight_start":49,"highlight_end":51}],"label":"expected `&str`, found `&Box<dyn Error>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_integration.rs","byte_start":986,"byte_end":1000,"line_start":27,"line_end":27,"column_start":34,"column_end":48,"is_primary":false,"text":[{"text":"                let error_type = classify_error(&e);","highlight_start":34,"highlight_end":48}],"label":"arguments to this function are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected reference `&str`\n   found reference `&Box<dyn std::error::Error>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"function defined here","code":null,"level":"note","spans":[{"file_name":"tests/test_integration.rs","byte_start":7141,"byte_end":7152,"line_start":186,"line_end":186,"column_start":19,"column_end":30,"is_primary":false,"text":[{"text":"fn classify_error(error: &str) -> String {","highlight_start":19,"highlight_end":30}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_integration.rs","byte_start":7126,"byte_end":7140,"line_start":186,"line_end":186,"column_start":4,"column_end":18,"is_primary":true,"text":[{"text":"fn classify_error(error: &str) -> String {","highlight_start":4,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_integration.rs:27:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let error_type = classify_error(&e);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `&str`, found `&Box<dyn Error>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12marguments to this function are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35mstr\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35mBox<dyn std::error::Error>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_integration.rs:186:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn classify_error(error: &str) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`?` couldn't convert the error: `wasm_bindgen::JsValue: std::error::Error` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"tests/test_integration.rs","byte_start":6315,"byte_end":6338,"line_start":163,"line_end":163,"column_start":26,"column_end":49,"is_primary":false,"text":[{"text":"    let _custom_result = decode_entries(0, data)?;","highlight_start":26,"highlight_end":49}],"label":"this has type `Result<_, wasm_bindgen::JsValue>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_integration.rs","byte_start":6174,"byte_end":6222,"line_start":159,"line_end":159,"column_start":47,"column_end":95,"is_primary":false,"text":[{"text":"fn measure_decode_performance(data: &[u8]) -> Result<(u128, u128), Box<dyn std::error::Error>> {","highlight_start":47,"highlight_end":95}],"label":"required `wasm_bindgen::JsValue: std::error::Error` because of this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_integration.rs","byte_start":6338,"byte_end":6339,"line_start":163,"line_end":163,"column_start":49,"column_end":50,"is_primary":true,"text":[{"text":"    let _custom_result = decode_entries(0, data)?;","highlight_start":49,"highlight_end":50}],"label":"the trait `std::error::Error` is not implemented for `wasm_bindgen::JsValue`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"tests/test_integration.rs","byte_start":6338,"byte_end":6339,"line_start":163,"line_end":163,"column_start":49,"column_end":50,"is_primary":false,"text":[{"text":"    let _custom_result = decode_entries(0, data)?;","highlight_start":49,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of operator `?`","def_site_span":{"file_name":"tests/test_integration.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for `Box<dyn std::error::Error>` to implement `From<wasm_bindgen::JsValue>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: `?` couldn't convert the error: `wasm_bindgen::JsValue: std::error::Error` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_integration.rs:163:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn measure_decode_performance(data: &[u8]) -> Result<(u128, u128), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired `wasm_bindgen::JsValue: std::error::Error` because of this\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let _custom_result = decode_entries(0, data)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `std::error::Error` is not implemented for `wasm_bindgen::JsValue`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis has type `Result<_, wasm_bindgen::JsValue>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `Box<dyn std::error::Error>` to implement `From<wasm_bindgen::JsValue>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`?` couldn't convert the error: `wasm_bindgen::JsValue: std::error::Error` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"tests/test_integration.rs","byte_start":6721,"byte_end":6744,"line_start":174,"line_end":174,"column_start":25,"column_end":48,"is_primary":false,"text":[{"text":"    let custom_result = decode_entries(0, data)?;","highlight_start":25,"highlight_end":48}],"label":"this has type `Result<_, wasm_bindgen::JsValue>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_integration.rs","byte_start":6656,"byte_end":6694,"line_start":173,"line_end":173,"column_start":54,"column_end":92,"is_primary":false,"text":[{"text":"fn test_memory_usage(data: &[u8], filename: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":54,"highlight_end":92}],"label":"required `wasm_bindgen::JsValue: std::error::Error` because of this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_integration.rs","byte_start":6744,"byte_end":6745,"line_start":174,"line_end":174,"column_start":48,"column_end":49,"is_primary":true,"text":[{"text":"    let custom_result = decode_entries(0, data)?;","highlight_start":48,"highlight_end":49}],"label":"the trait `std::error::Error` is not implemented for `wasm_bindgen::JsValue`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"tests/test_integration.rs","byte_start":6744,"byte_end":6745,"line_start":174,"line_end":174,"column_start":48,"column_end":49,"is_primary":false,"text":[{"text":"    let custom_result = decode_entries(0, data)?;","highlight_start":48,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of operator `?`","def_site_span":{"file_name":"tests/test_integration.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for `Box<dyn std::error::Error>` to implement `From<wasm_bindgen::JsValue>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: `?` couldn't convert the error: `wasm_bindgen::JsValue: std::error::Error` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_integration.rs:174:48\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn test_memory_usage(data: &[u8], filename: &str) -> Result<(), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired `wasm_bindgen::JsValue: std::error::Error` because of this\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let custom_result = decode_entries(0, data)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `std::error::Error` is not implemented for `wasm_bindgen::JsValue`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mthis has type `Result<_, wasm_bindgen::JsValue>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the question mark operation (`?`) implicitly performs a conversion on the error value using the `From` trait\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: required for `Box<dyn std::error::Error>` to implement `From<wasm_bindgen::JsValue>`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 3 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 3 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0277, E0308.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0277, E0308.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0277`.\u001b[0m\n"}
