{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `shredstream_decoder`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":85,"byte_end":104,"line_start":6,"line_end":6,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use shredstream_decoder::types::{Entry as CustomEntry, CompiledInstruction as CustomInstruction};","highlight_start":5,"highlight_end":24}],"label":"use of unresolved module or unlinked crate `shredstream_decoder`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `shredstream_decoder`, use `cargo add shredstream_decoder` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `shredstream_decoder`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse shredstream_decoder::types::{Entry as CustomEntry, CompiledInstruction as CustomInstruction};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `shredstream_decoder`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `shredstream_decoder`, use `cargo add shredstream_decoder` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `shredstream_decoder`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":7545,"byte_end":7564,"line_start":173,"line_end":173,"column_start":18,"column_end":37,"is_primary":true,"text":[{"text":"    custom_msg: &shredstream_decoder::types::VersionedMessage,","highlight_start":18,"highlight_end":37}],"label":"use of unresolved module or unlinked crate `shredstream_decoder`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `shredstream_decoder`, use `cargo add shredstream_decoder` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `shredstream_decoder`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:173:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    custom_msg: &shredstream_decoder::types::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `shredstream_decoder`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `shredstream_decoder`, use `cargo add shredstream_decoder` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing lifetime specifiers","code":{"code":"E0106","explanation":"This error indicates that a lifetime is missing from a type. If it is an error\ninside a function signature, the problem may be with failing to adhere to the\nlifetime elision rules (see below).\n\nErroneous code examples:\n\n```compile_fail,E0106\nstruct Foo1 { x: &bool }\n              // ^ expected lifetime parameter\nstruct Foo2<'a> { x: &'a bool } // correct\n\nstruct Bar1 { x: Foo2 }\n              // ^^^^ expected lifetime parameter\nstruct Bar2<'a> { x: Foo2<'a> } // correct\n\nenum Baz1 { A(u8), B(&bool), }\n                  // ^ expected lifetime parameter\nenum Baz2<'a> { A(u8), B(&'a bool), } // correct\n\ntype MyStr1 = &str;\n           // ^ expected lifetime parameter\ntype MyStr2<'a> = &'a str; // correct\n```\n\nLifetime elision is a special, limited kind of inference for lifetimes in\nfunction signatures which allows you to leave out lifetimes in certain cases.\nFor more background on lifetime elision see [the book][book-le].\n\nThe lifetime elision rules require that any function signature with an elided\noutput lifetime must either have:\n\n - exactly one input lifetime\n - or, multiple input lifetimes, but the function must also be a method with a\n   `&self` or `&mut self` receiver\n\nIn the first case, the output lifetime is inferred to be the same as the unique\ninput lifetime. In the second case, the lifetime is instead inferred to be the\nsame as the lifetime on `&self` or `&mut self`.\n\nHere are some examples of elision errors:\n\n```compile_fail,E0106\n// error, no input lifetimes\nfn foo() -> &str { }\n\n// error, `x` and `y` have distinct lifetimes inferred\nfn bar(x: &str, y: &str) -> &str { }\n\n// error, `y`'s lifetime is inferred to be distinct from `x`'s\nfn baz<'a>(x: &'a str, y: &str) -> &str { }\n```\n\n[book-le]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":7662,"byte_end":7663,"line_start":175,"line_end":175,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomInstruction>, &Vec<solana_entry::entry::CompiledInstruction>), Box<dyn std::error::Error>> {","highlight_start":14,"highlight_end":15}],"label":"expected named lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_instructions.rs","byte_start":7687,"byte_end":7688,"line_start":175,"line_end":175,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomInstruction>, &Vec<solana_entry::entry::CompiledInstruction>), Box<dyn std::error::Error>> {","highlight_start":39,"highlight_end":40}],"label":"expected named lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_instructions.rs","byte_start":7544,"byte_end":7589,"line_start":173,"line_end":173,"column_start":17,"column_end":62,"is_primary":false,"text":[{"text":"    custom_msg: &shredstream_decoder::types::VersionedMessage,","highlight_start":17,"highlight_end":62}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_instructions.rs","byte_start":7609,"byte_end":7647,"line_start":174,"line_end":174,"column_start":19,"column_end":57,"is_primary":false,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":19,"highlight_end":57}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this function's return type contains a borrowed value, but the signature does not say whether it is borrowed from `custom_msg` or `official_msg`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider introducing a named lifetime parameter","code":null,"level":"help","spans":[{"file_name":"tests/test_instructions.rs","byte_start":7526,"byte_end":7526,"line_start":172,"line_end":172,"column_start":24,"column_end":24,"is_primary":true,"text":[{"text":"fn extract_instructions(","highlight_start":24,"highlight_end":24}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_instructions.rs","byte_start":7663,"byte_end":7663,"line_start":175,"line_end":175,"column_start":15,"column_end":15,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomInstruction>, &Vec<solana_entry::entry::CompiledInstruction>), Box<dyn std::error::Error>> {","highlight_start":15,"highlight_end":15}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_instructions.rs","byte_start":7688,"byte_end":7688,"line_start":175,"line_end":175,"column_start":40,"column_end":40,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomInstruction>, &Vec<solana_entry::entry::CompiledInstruction>), Box<dyn std::error::Error>> {","highlight_start":40,"highlight_end":40}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_instructions.rs","byte_start":7545,"byte_end":7545,"line_start":173,"line_end":173,"column_start":18,"column_end":18,"is_primary":true,"text":[{"text":"    custom_msg: &shredstream_decoder::types::VersionedMessage,","highlight_start":18,"highlight_end":18}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_instructions.rs","byte_start":7610,"byte_end":7610,"line_start":174,"line_end":174,"column_start":20,"column_end":20,"is_primary":true,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":20,"highlight_end":20}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0106]\u001b[0m\u001b[0m\u001b[1m: missing lifetime specifiers\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:175:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    custom_msg: &shredstream_decoder::types::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official_msg: &solana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(&Vec<CustomInstruction>, &Vec<solana_entry::entry::CompiledInstruction>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: this function's return type contains a borrowed value, but the signature does not say whether it is borrowed from `custom_msg` or `official_msg`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider introducing a named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0mfn extract_instructions\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    custom_msg: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mshredstream_decoder::types::VersionedMessage,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    official_msg: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0msolana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m) -> Result<(&\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mVec<CustomInstruction>, &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mVec<solana_entry::entry::CompiledInstruction>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `shredstream_decoder`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":7814,"byte_end":7833,"line_start":177,"line_end":177,"column_start":10,"column_end":29,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::Legacy(custom), solana_entry::entry::VersionedMessage::Legacy(official)) => {","highlight_start":10,"highlight_end":29}],"label":"use of unresolved module or unlinked crate `shredstream_decoder`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `shredstream_decoder`, use `cargo add shredstream_decoder` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `shredstream_decoder`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:177:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::Legacy(custom), solana_entry::entry::VersionedMessage::Legacy(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `shredstream_decoder`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `shredstream_decoder`, use `cargo add shredstream_decoder` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `VersionedMessage` in `entry`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":7897,"byte_end":7913,"line_start":177,"line_end":177,"column_start":93,"column_end":109,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::Legacy(custom), solana_entry::entry::VersionedMessage::Legacy(official)) => {","highlight_start":93,"highlight_end":109}],"label":"could not find `VersionedMessage` in `entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: could not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:177:93\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::Legacy(custom), solana_entry::entry::VersionedMessage::Legacy(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `VersionedMessage` in `entry`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `shredstream_decoder`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":8020,"byte_end":8039,"line_start":180,"line_end":180,"column_start":10,"column_end":29,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {","highlight_start":10,"highlight_end":29}],"label":"use of unresolved module or unlinked crate `shredstream_decoder`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `shredstream_decoder`, use `cargo add shredstream_decoder` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: use of unresolved module or unlinked crate `shredstream_decoder`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:180:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m180\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `shredstream_decoder`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `shredstream_decoder`, use `cargo add shredstream_decoder` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `VersionedMessage` in `entry`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":8099,"byte_end":8115,"line_start":180,"line_end":180,"column_start":89,"column_end":105,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {","highlight_start":89,"highlight_end":105}],"label":"could not find `VersionedMessage` in `entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: could not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:180:89\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m180\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `VersionedMessage` in `entry`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `VersionedMessage` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":7631,"byte_end":7647,"line_start":174,"line_end":174,"column_start":41,"column_end":57,"is_primary":true,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":41,"highlight_end":57}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `VersionedMessage` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:174:41\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m174\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official_msg: &solana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `CompiledInstruction` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":7713,"byte_end":7732,"line_start":175,"line_end":175,"column_start":65,"column_end":84,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomInstruction>, &Vec<solana_entry::entry::CompiledInstruction>), Box<dyn std::error::Error>> {","highlight_start":65,"highlight_end":84}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `CompiledInstruction` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:175:65\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m175\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(&Vec<CustomInstruction>, &Vec<solana_entry::entry::CompiledInstruction>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `CompiledInstruction` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_instructions.rs","byte_start":8370,"byte_end":8389,"line_start":189,"line_end":189,"column_start":37,"column_end":56,"is_primary":true,"text":[{"text":"    official: &solana_entry::entry::CompiledInstruction,","highlight_start":37,"highlight_end":56}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `CompiledInstruction` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_instructions.rs:189:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official: &solana_entry::entry::CompiledInstruction,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 10 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 10 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0106, E0412, E0433.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0106, E0412, E0433.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0106`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0106`.\u001b[0m\n"}
