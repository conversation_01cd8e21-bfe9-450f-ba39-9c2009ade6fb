{"$message_type":"diagnostic","message":"missing lifetime specifiers","code":{"code":"E0106","explanation":"This error indicates that a lifetime is missing from a type. If it is an error\ninside a function signature, the problem may be with failing to adhere to the\nlifetime elision rules (see below).\n\nErroneous code examples:\n\n```compile_fail,E0106\nstruct Foo1 { x: &bool }\n              // ^ expected lifetime parameter\nstruct Foo2<'a> { x: &'a bool } // correct\n\nstruct Bar1 { x: Foo2 }\n              // ^^^^ expected lifetime parameter\nstruct Bar2<'a> { x: Foo2<'a> } // correct\n\nenum Baz1 { A(u8), B(&bool), }\n                  // ^ expected lifetime parameter\nenum Baz2<'a> { A(u8), B(&'a bool), } // correct\n\ntype MyStr1 = &str;\n           // ^ expected lifetime parameter\ntype MyStr2<'a> = &'a str; // correct\n```\n\nLifetime elision is a special, limited kind of inference for lifetimes in\nfunction signatures which allows you to leave out lifetimes in certain cases.\nFor more background on lifetime elision see [the book][book-le].\n\nThe lifetime elision rules require that any function signature with an elided\noutput lifetime must either have:\n\n - exactly one input lifetime\n - or, multiple input lifetimes, but the function must also be a method with a\n   `&self` or `&mut self` receiver\n\nIn the first case, the output lifetime is inferred to be the same as the unique\ninput lifetime. In the second case, the lifetime is instead inferred to be the\nsame as the lifetime on `&self` or `&mut self`.\n\nHere are some examples of elision errors:\n\n```compile_fail,E0106\n// error, no input lifetimes\nfn foo() -> &str { }\n\n// error, `x` and `y` have distinct lifetimes inferred\nfn bar(x: &str, y: &str) -> &str { }\n\n// error, `y`'s lifetime is inferred to be distinct from `x`'s\nfn baz<'a>(x: &'a str, y: &str) -> &str { }\n```\n\n[book-le]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8118,"byte_end":8119,"line_start":190,"line_end":190,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomPubkey>, &Vec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {","highlight_start":14,"highlight_end":15}],"label":"expected named lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8138,"byte_end":8139,"line_start":190,"line_end":190,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomPubkey>, &Vec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {","highlight_start":34,"highlight_end":35}],"label":"expected named lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8000,"byte_end":8045,"line_start":188,"line_end":188,"column_start":17,"column_end":62,"is_primary":false,"text":[{"text":"    custom_msg: &shredstream_decoder::types::VersionedMessage,","highlight_start":17,"highlight_end":62}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8065,"byte_end":8103,"line_start":189,"line_end":189,"column_start":19,"column_end":57,"is_primary":false,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":19,"highlight_end":57}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this function's return type contains a borrowed value, but the signature does not say whether it is borrowed from `custom_msg` or `official_msg`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider introducing a named lifetime parameter","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":7982,"byte_end":7982,"line_start":187,"line_end":187,"column_start":24,"column_end":24,"is_primary":true,"text":[{"text":"fn extract_account_keys(","highlight_start":24,"highlight_end":24}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8119,"byte_end":8119,"line_start":190,"line_end":190,"column_start":15,"column_end":15,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomPubkey>, &Vec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {","highlight_start":15,"highlight_end":15}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8139,"byte_end":8139,"line_start":190,"line_end":190,"column_start":35,"column_end":35,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomPubkey>, &Vec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {","highlight_start":35,"highlight_end":35}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8001,"byte_end":8001,"line_start":188,"line_end":188,"column_start":18,"column_end":18,"is_primary":true,"text":[{"text":"    custom_msg: &shredstream_decoder::types::VersionedMessage,","highlight_start":18,"highlight_end":18}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8066,"byte_end":8066,"line_start":189,"line_end":189,"column_start":20,"column_end":20,"is_primary":true,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":20,"highlight_end":20}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0106]\u001b[0m\u001b[0m\u001b[1m: missing lifetime specifiers\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:190:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    custom_msg: &shredstream_decoder::types::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official_msg: &solana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(&Vec<CustomPubkey>, &Vec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: this function's return type contains a borrowed value, but the signature does not say whether it is borrowed from `custom_msg` or `official_msg`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider introducing a named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0mfn extract_account_keys\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    custom_msg: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mshredstream_decoder::types::VersionedMessage,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    official_msg: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0msolana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m) -> Result<(&\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mVec<CustomPubkey>, &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mVec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing lifetime specifiers","code":{"code":"E0106","explanation":"This error indicates that a lifetime is missing from a type. If it is an error\ninside a function signature, the problem may be with failing to adhere to the\nlifetime elision rules (see below).\n\nErroneous code examples:\n\n```compile_fail,E0106\nstruct Foo1 { x: &bool }\n              // ^ expected lifetime parameter\nstruct Foo2<'a> { x: &'a bool } // correct\n\nstruct Bar1 { x: Foo2 }\n              // ^^^^ expected lifetime parameter\nstruct Bar2<'a> { x: Foo2<'a> } // correct\n\nenum Baz1 { A(u8), B(&bool), }\n                  // ^ expected lifetime parameter\nenum Baz2<'a> { A(u8), B(&'a bool), } // correct\n\ntype MyStr1 = &str;\n           // ^ expected lifetime parameter\ntype MyStr2<'a> = &'a str; // correct\n```\n\nLifetime elision is a special, limited kind of inference for lifetimes in\nfunction signatures which allows you to leave out lifetimes in certain cases.\nFor more background on lifetime elision see [the book][book-le].\n\nThe lifetime elision rules require that any function signature with an elided\noutput lifetime must either have:\n\n - exactly one input lifetime\n - or, multiple input lifetimes, but the function must also be a method with a\n   `&self` or `&mut self` receiver\n\nIn the first case, the output lifetime is inferred to be the same as the unique\ninput lifetime. In the second case, the lifetime is instead inferred to be the\nsame as the lifetime on `&self` or `&mut self`.\n\nHere are some examples of elision errors:\n\n```compile_fail,E0106\n// error, no input lifetimes\nfn foo() -> &str { }\n\n// error, `x` and `y` have distinct lifetimes inferred\nfn bar(x: &str, y: &str) -> &str { }\n\n// error, `y`'s lifetime is inferred to be distinct from `x`'s\nfn baz<'a>(x: &'a str, y: &str) -> &str { }\n```\n\n[book-le]: https://doc.rust-lang.org/book/ch10-03-lifetime-syntax.html#lifetime-elision\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8875,"byte_end":8876,"line_start":205,"line_end":205,"column_start":21,"column_end":22,"is_primary":true,"text":[{"text":") -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {","highlight_start":21,"highlight_end":22}],"label":"expected named lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8903,"byte_end":8904,"line_start":205,"line_end":205,"column_start":49,"column_end":50,"is_primary":true,"text":[{"text":") -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {","highlight_start":49,"highlight_end":50}],"label":"expected named lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8750,"byte_end":8795,"line_start":203,"line_end":203,"column_start":17,"column_end":62,"is_primary":false,"text":[{"text":"    custom_msg: &shredstream_decoder::types::VersionedMessage,","highlight_start":17,"highlight_end":62}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8815,"byte_end":8853,"line_start":204,"line_end":204,"column_start":19,"column_end":57,"is_primary":false,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":19,"highlight_end":57}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"this function's return type contains a borrowed value, but the signature does not say whether it is borrowed from `custom_msg` or `official_msg`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"consider introducing a named lifetime parameter","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8732,"byte_end":8732,"line_start":202,"line_end":202,"column_start":27,"column_end":27,"is_primary":true,"text":[{"text":"fn extract_address_lookups(","highlight_start":27,"highlight_end":27}],"label":null,"suggested_replacement":"<'a>","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8876,"byte_end":8876,"line_start":205,"line_end":205,"column_start":22,"column_end":22,"is_primary":true,"text":[{"text":") -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {","highlight_start":22,"highlight_end":22}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8904,"byte_end":8904,"line_start":205,"line_end":205,"column_start":50,"column_end":50,"is_primary":true,"text":[{"text":") -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {","highlight_start":50,"highlight_end":50}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8751,"byte_end":8751,"line_start":203,"line_end":203,"column_start":18,"column_end":18,"is_primary":true,"text":[{"text":"    custom_msg: &shredstream_decoder::types::VersionedMessage,","highlight_start":18,"highlight_end":18}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"tests/test_accounts.rs","byte_start":8816,"byte_end":8816,"line_start":204,"line_end":204,"column_start":20,"column_end":20,"is_primary":true,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":20,"highlight_end":20}],"label":null,"suggested_replacement":"'a ","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0106]\u001b[0m\u001b[0m\u001b[1m: missing lifetime specifiers\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:205:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    custom_msg: &shredstream_decoder::types::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official_msg: &solana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: this function's return type contains a borrowed value, but the signature does not say whether it is borrowed from `custom_msg` or `official_msg`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider introducing a named lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m202\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0mfn extract_address_lookups\u001b[0m\u001b[0m\u001b[38;5;10m<'a>\u001b[0m\u001b[0m(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    custom_msg: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mshredstream_decoder::types::VersionedMessage,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    official_msg: &\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0msolana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m) -> Result<(Option<&\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mVec<CustomLookup>>, Option<&\u001b[0m\u001b[0m\u001b[38;5;10m'a \u001b[0m\u001b[0mVec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `VersionedMessage` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8087,"byte_end":8103,"line_start":189,"line_end":189,"column_start":41,"column_end":57,"is_primary":true,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":41,"highlight_end":57}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this enum","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::VersionedMessage;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `VersionedMessage`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8066,"byte_end":8087,"line_start":189,"line_end":189,"column_start":20,"column_end":41,"is_primary":true,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":20,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `VersionedMessage` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:189:41\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official_msg: &solana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::VersionedMessage;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `VersionedMessage`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    official_msg: &\u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mVersionedMessage,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    official_msg: &VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `Pubkey` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8164,"byte_end":8170,"line_start":190,"line_end":190,"column_start":60,"column_end":66,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomPubkey>, &Vec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {","highlight_start":60,"highlight_end":66}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::Pubkey;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `Pubkey`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8143,"byte_end":8164,"line_start":190,"line_end":190,"column_start":39,"column_end":60,"is_primary":true,"text":[{"text":") -> Result<(&Vec<CustomPubkey>, &Vec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {","highlight_start":39,"highlight_end":60}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `Pubkey` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:190:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(&Vec<CustomPubkey>, &Vec<solana_entry::entry::Pubkey>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::Pubkey;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `Pubkey`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m) -> Result<(&Vec<CustomPubkey>, &Vec<\u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mPubkey>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m) -> Result<(&Vec<CustomPubkey>, &Vec<Pubkey>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `VersionedMessage` in `entry`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8335,"byte_end":8351,"line_start":192,"line_end":192,"column_start":93,"column_end":109,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::Legacy(custom), solana_entry::entry::VersionedMessage::Legacy(official)) => {","highlight_start":93,"highlight_end":109}],"label":"could not find `VersionedMessage` in `entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this enum","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::VersionedMessage;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `VersionedMessage`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8314,"byte_end":8335,"line_start":192,"line_end":192,"column_start":72,"column_end":93,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::Legacy(custom), solana_entry::entry::VersionedMessage::Legacy(official)) => {","highlight_start":72,"highlight_end":93}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: could not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:192:93\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::Legacy(custom), solana_entry::entry::VersionedMessage::Legacy(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::VersionedMessage;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `VersionedMessage`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::Legacy(custom), \u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mVersionedMessage::Legacy(official)) => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m192\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::Legacy(custom), VersionedMessage::Legacy(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `VersionedMessage` in `entry`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8537,"byte_end":8553,"line_start":195,"line_end":195,"column_start":89,"column_end":105,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {","highlight_start":89,"highlight_end":105}],"label":"could not find `VersionedMessage` in `entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this enum","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::VersionedMessage;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `VersionedMessage`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8516,"byte_end":8537,"line_start":195,"line_end":195,"column_start":68,"column_end":89,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {","highlight_start":68,"highlight_end":89}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: could not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:195:89\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::VersionedMessage;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `VersionedMessage`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::V0(custom), \u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mVersionedMessage::V0(official)) => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::V0(custom), VersionedMessage::V0(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `VersionedMessage` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8837,"byte_end":8853,"line_start":204,"line_end":204,"column_start":41,"column_end":57,"is_primary":true,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":41,"highlight_end":57}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this enum","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::VersionedMessage;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `VersionedMessage`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8816,"byte_end":8837,"line_start":204,"line_end":204,"column_start":20,"column_end":41,"is_primary":true,"text":[{"text":"    official_msg: &solana_entry::entry::VersionedMessage,","highlight_start":20,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `VersionedMessage` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:204:41\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official_msg: &solana_entry::entry::VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::VersionedMessage;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `VersionedMessage`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    official_msg: &\u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mVersionedMessage,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    official_msg: &VersionedMessage,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `MessageAddressTableLookup` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8929,"byte_end":8954,"line_start":205,"line_end":205,"column_start":75,"column_end":100,"is_primary":true,"text":[{"text":") -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {","highlight_start":75,"highlight_end":100}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::MessageAddressTableLookup;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `MessageAddressTableLookup`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":8908,"byte_end":8929,"line_start":205,"line_end":205,"column_start":54,"column_end":75,"is_primary":true,"text":[{"text":") -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {","highlight_start":54,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `MessageAddressTableLookup` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:205:75\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<solana_entry::entry::MessageAddressTableLookup>>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::MessageAddressTableLookup;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `MessageAddressTableLookup`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m) -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<\u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mMessageAddressTableLookup>>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m) -> Result<(Option<&Vec<CustomLookup>>, Option<&Vec<MessageAddressTableLookup>>), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `VersionedMessage` in `entry`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":9115,"byte_end":9131,"line_start":207,"line_end":207,"column_start":88,"column_end":104,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::Legacy(_), solana_entry::entry::VersionedMessage::Legacy(_)) => {","highlight_start":88,"highlight_end":104}],"label":"could not find `VersionedMessage` in `entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this enum","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::VersionedMessage;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `VersionedMessage`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":9094,"byte_end":9115,"line_start":207,"line_end":207,"column_start":67,"column_end":88,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::Legacy(_), solana_entry::entry::VersionedMessage::Legacy(_)) => {","highlight_start":67,"highlight_end":88}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: could not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:207:88\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::Legacy(_), solana_entry::entry::VersionedMessage::Legacy(_)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::VersionedMessage;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `VersionedMessage`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::Legacy(_), \u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mVersionedMessage::Legacy(_)) => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::Legacy(_), VersionedMessage::Legacy(_)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `VersionedMessage` in `entry`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":9276,"byte_end":9292,"line_start":210,"line_end":210,"column_start":89,"column_end":105,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {","highlight_start":89,"highlight_end":105}],"label":"could not find `VersionedMessage` in `entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this enum","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::VersionedMessage;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `VersionedMessage`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":9255,"byte_end":9276,"line_start":210,"line_end":210,"column_start":68,"column_end":89,"is_primary":true,"text":[{"text":"        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {","highlight_start":68,"highlight_end":89}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m: failed to resolve: could not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:210:89\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::V0(custom), solana_entry::entry::VersionedMessage::V0(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `VersionedMessage` in `entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this enum\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::VersionedMessage;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `VersionedMessage`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::V0(custom), \u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mVersionedMessage::V0(official)) => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        (shredstream_decoder::types::VersionedMessage::V0(custom), VersionedMessage::V0(official)) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `Pubkey` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":9567,"byte_end":9573,"line_start":219,"line_end":219,"column_start":37,"column_end":43,"is_primary":true,"text":[{"text":"    official: &solana_entry::entry::Pubkey,","highlight_start":37,"highlight_end":43}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::Pubkey;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `Pubkey`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":9546,"byte_end":9567,"line_start":219,"line_end":219,"column_start":16,"column_end":37,"is_primary":true,"text":[{"text":"    official: &solana_entry::entry::Pubkey,","highlight_start":16,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `Pubkey` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:219:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m219\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official: &solana_entry::entry::Pubkey,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::Pubkey;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `Pubkey`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m219\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    official: &\u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mPubkey,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m219\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    official: &Pubkey,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"cannot find type `MessageAddressTableLookup` in module `solana_entry::entry`","code":{"code":"E0412","explanation":"A used type name is not in scope.\n\nErroneous code examples:\n\n```compile_fail,E0412\nimpl Something {} // error: type name `Something` is not in scope\n\n// or:\n\ntrait Foo {\n    fn bar(N); // error: type name `N` is not in scope\n}\n\n// or:\n\nfn foo(x: T) {} // type name `T` is not in scope\n```\n\nTo fix this error, please verify you didn't misspell the type name, you did\ndeclare it or imported it into the scope. Examples:\n\n```\nstruct Something;\n\nimpl Something {} // ok!\n\n// or:\n\ntrait Foo {\n    type N;\n\n    fn bar(_: Self::N); // ok!\n}\n\n// or:\n\nfn foo<T>(x: T) {} // ok!\n```\n\nAnother case that causes this error is when a type is imported into a parent\nmodule. To fix this, you can follow the suggestion and use File directly or\n`use super::File;` which will import the types from the parent namespace. An\nexample that causes this error is below:\n\n```compile_fail,E0412\nuse std::fs::File;\n\nmod foo {\n    fn some_function(f: File) {}\n}\n```\n\n```\nuse std::fs::File;\n\nmod foo {\n    // either\n    use super::File;\n    // or\n    // use std::fs::File;\n    fn foo(f: File) {}\n}\n# fn main() {} // don't insert it for us; that'll break imports\n```\n"},"level":"error","spans":[{"file_name":"tests/test_accounts.rs","byte_start":10024,"byte_end":10049,"line_start":237,"line_end":237,"column_start":37,"column_end":62,"is_primary":true,"text":[{"text":"    official: &solana_entry::entry::MessageAddressTableLookup,","highlight_start":37,"highlight_end":62}],"label":"not found in `solana_entry::entry`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":13,"byte_end":13,"line_start":3,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use bincode;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use shredstream_decoder::MessageAddressTableLookup;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `MessageAddressTableLookup`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"tests/test_accounts.rs","byte_start":10003,"byte_end":10024,"line_start":237,"line_end":237,"column_start":16,"column_end":37,"is_primary":true,"text":[{"text":"    official: &solana_entry::entry::MessageAddressTableLookup,","highlight_start":16,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0412]\u001b[0m\u001b[0m\u001b[1m: cannot find type `MessageAddressTableLookup` in module `solana_entry::entry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mtests/test_accounts.rs:237:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m237\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    official: &solana_entry::entry::MessageAddressTableLookup,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mnot found in `solana_entry::entry`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use shredstream_decoder::MessageAddressTableLookup;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `MessageAddressTableLookup`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m237\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    official: &\u001b[0m\u001b[0m\u001b[38;5;9msolana_entry::entry::\u001b[0m\u001b[0mMessageAddressTableLookup,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m237\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    official: &MessageAddressTableLookup,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 12 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 12 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0106, E0412, E0433.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0106, E0412, E0433.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0106`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0106`.\u001b[0m\n"}
