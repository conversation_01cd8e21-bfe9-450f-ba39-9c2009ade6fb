{"rustc": 15497389221046826682, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 3882023571813807903, "profile": 3033921117576893, "path": 11967964535478927124, "deps": [[99783594999256520, "prost_build", false, 2913766583819479551], [3060637413840920116, "proc_macro2", false, 9689789723025982531], [8549471757621926118, "prettyplease", false, 14667074247675628943], [17990358020177143287, "quote", false, 16463695800006207511], [18149961000318489080, "syn", false, 8548565062926475254]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tonic-build-3016c0a7a5ef086e/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}