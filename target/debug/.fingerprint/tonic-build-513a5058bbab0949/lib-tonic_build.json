{"rustc": 15497389221046826682, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 3882023571813807903, "profile": 3033921117576893, "path": 11967964535478927124, "deps": [[99783594999256520, "prost_build", false, 10597513459089278159], [3060637413840920116, "proc_macro2", false, 9689789723025982531], [8549471757621926118, "prettyplease", false, 611319959886878449], [17990358020177143287, "quote", false, 16463695800006207511], [18149961000318489080, "syn", false, 5371482563176609584]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tonic-build-513a5058bbab0949/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}