{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 3165595516910038244, "profile": 5347358027863023418, "path": 14698983297451476235, "deps": [[784494742817713399, "tower_service", false, 7067141016842501417], [4405182208873388884, "http", false, 4251333103881920354], [7712452662827335977, "tower_layer", false, 16585682252649650789], [8915503303801890683, "http_body", false, 3503627302696093547], [9293824762099617471, "build_script_build", false, 1672304555263872995], [10229185211513642314, "mime", false, 7335903177883505045], [10629569228670356391, "futures_util", false, 6465709987138131105], [11946729385090170470, "async_trait", false, 15978247741688482696], [16066129441945555748, "bytes", false, 1910611532240841935]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-core-6512032371797858/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}