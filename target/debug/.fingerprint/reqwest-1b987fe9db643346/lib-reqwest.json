{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"deflate\", \"gzip\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"tokio-util\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5347358027863023418, "path": 12360358679216226155, "deps": [[40386456601120721, "percent_encoding", false, 11021943286677184330], [95042085696191081, "ipnet", false, 13825495589799560805], [126872836426101300, "async_compression", false, 15349329816777707963], [264090853244900308, "sync_wrapper", false, 3875097257802466094], [784494742817713399, "tower_service", false, 7067141016842501417], [1044435446100926395, "hyper_rustls", false, 4902879881317729078], [1288403060204016458, "tokio_util", false, 3631256788999165529], [1906322745568073236, "pin_project_lite", false, 10604144968048648353], [3150220818285335163, "url", false, 15413762187481604315], [3722963349756955755, "once_cell", false, 263002596821618790], [4405182208873388884, "http", false, 4251333103881920354], [5986029879202738730, "log", false, 8579711904470847511], [7414427314941361239, "hyper", false, 4038210753936793931], [7620660491849607393, "futures_core", false, 2045025667838836927], [8915503303801890683, "http_body", false, 3503627302696093547], [9538054652646069845, "tokio", false, 4173230681869071208], [9689903380558560274, "serde", false, 387872419310613385], [10229185211513642314, "mime", false, 7335903177883505045], [10629569228670356391, "futures_util", false, 6465709987138131105], [11107720164717273507, "system_configuration", false, 15021569812877602997], [11295624341523567602, "rustls", false, 5368133967618969728], [13809605890706463735, "h2", false, 13706593031513124946], [14564311161534545801, "encoding_rs", false, 16727783803130639954], [15367738274754116744, "serde_json", false, 1367732154254617764], [16066129441945555748, "bytes", false, 1910611532240841935], [16311359161338405624, "rustls_pemfile", false, 16446645927696082920], [16542808166767769916, "serde_urlencoded", false, 13554091556675595818], [16622232390123975175, "tokio_rustls", false, 18405712488831691749], [17652733826348741533, "webpki_roots", false, 17011804013500109333], [18066890886671768183, "base64", false, 10097053095721575341]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-1b987fe9db643346/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}