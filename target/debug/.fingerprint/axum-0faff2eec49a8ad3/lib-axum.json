{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 12074263998246110377, "profile": 5347358027863023418, "path": 2695021009374120339, "deps": [[40386456601120721, "percent_encoding", false, 11021943286677184330], [264090853244900308, "sync_wrapper", false, 3875097257802466094], [784494742817713399, "tower_service", false, 7067141016842501417], [1906322745568073236, "pin_project_lite", false, 10604144968048648353], [3129130049864710036, "memchr", false, 3690843730329462536], [3601586811267292532, "tower", false, 6968304526701783547], [4405182208873388884, "http", false, 4251333103881920354], [7414427314941361239, "hyper", false, 17027292415345813965], [7695812897323945497, "itoa", false, 5961067143038888597], [7712452662827335977, "tower_layer", false, 16585682252649650789], [8915503303801890683, "http_body", false, 3503627302696093547], [9293824762099617471, "axum_core", false, 15572986596005917666], [9678799920983747518, "matchit", false, 930280262509330325], [9689903380558560274, "serde", false, 2372294934450711281], [10229185211513642314, "mime", false, 7335903177883505045], [10435729446543529114, "bitflags", false, 5826885051677081119], [10629569228670356391, "futures_util", false, 4767363480226270355], [11946729385090170470, "async_trait", false, 9395561100880320866], [16066129441945555748, "bytes", false, 1910611532240841935], [16244562316228021087, "build_script_build", false, 18303946893724213748]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/axum-0faff2eec49a8ad3/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}