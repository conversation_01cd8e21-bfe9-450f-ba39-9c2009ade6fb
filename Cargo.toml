[package]
name = "shredstream-decoder"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
bincode = "1.3.3"
serde = { version = "1.0", features = ["derive"] }
serde-wasm-bindgen = "0.6"
solana-hash = { version = "2.2.1", default-features = false, features = [
    "serde",
] }
solana-signature = { version = "2.2.1", default-features = false, features = [
    "serde",
] }
solana-short-vec = { version = "2.2.1", default-features = false }
wasm-bindgen = { version = "0.2", features = ["serde-serialize"] }
tsify = "0.4"
wasm-bindgen-futures = "0.4"
js-sys = "0.3"
tokio = { version = "1.0", features = ["full"], optional = true }
solana-stream-sdk = { version = "0.2.5", optional = true }
dotenvy = { version = "0.15", optional = true }

[dev-dependencies]
solana-entry = { version = "2.2.7", default-features = false }
solana-transaction = { version = "=2.2.2", default-features = false }
solana-message = { version = "=2.2.1", default-features = false }
solana-pubkey = { version = "=2.2.1", default-features = false }
tokio = { version = "1.0", features = ["full"] }
solana-stream-sdk = "0.2.5"
dotenvy = "0.15"

[[bin]]
name = "shred_collector"
path = "bin/shred_collector.rs"
required-features = ["tokio", "solana-stream-sdk", "dotenvy"]
