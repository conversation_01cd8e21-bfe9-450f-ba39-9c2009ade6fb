{"version": "2.0.0", "tasks": [{"type": "cargo", "command": "build", "problemMatcher": ["$rustc"], "group": "build", "label": "Rust: cargo build"}, {"type": "cargo", "command": "build", "args": ["--release"], "problemMatcher": ["$rustc"], "group": "build", "label": "Rust: cargo build --release"}, {"type": "cargo", "command": "run", "problemMatcher": ["$rustc"], "group": "build", "label": "Rust: cargo run"}, {"type": "cargo", "command": "check", "problemMatcher": ["$rustc"], "group": "build", "label": "Rust: cargo check"}, {"type": "cargo", "command": "clippy", "args": ["--all-targets", "--all-features", "--", "-D", "warnings"], "problemMatcher": ["$rustc"], "group": "build", "label": "Rust: cargo clippy"}, {"type": "shell", "command": "rustfmt", "args": ["--check", "${file}"], "group": "build", "label": "Rust: format check current file", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"type": "shell", "command": "cargo", "args": ["fmt"], "group": "build", "label": "Rust: format all files", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"type": "shell", "command": "cargo", "args": ["clean"], "group": "build", "label": "Rust: cargo clean", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}