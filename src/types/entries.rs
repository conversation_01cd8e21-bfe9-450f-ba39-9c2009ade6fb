use serde::{Deserialize, Serialize};
use solana_hash::Hash;
use tsify::Tsify;

use super::transactions::VersionedTransaction;

#[derive(Serialize, Deserialize, Debug, Default, PartialEq, Eq, Clone, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct Entry {
    pub num_hashes: u64,
    pub hash: Hash,
    pub transactions: Vec<VersionedTransaction>,
}

#[derive(Serialize, Deserialize, Debug, Clone, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct ParsedEntry {
    pub slot: u64,
    pub entries: Vec<Entry>,
}
