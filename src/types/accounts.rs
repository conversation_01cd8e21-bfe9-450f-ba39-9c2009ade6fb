use serde::{Deserialize, Serialize};
use solana_short_vec as short_vec;
use tsify::Tsify;

#[derive(Serialize, Deserialize, Debug, <PERSON>ial<PERSON>q, Eq, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>h, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
#[repr(transparent)]
pub struct Pubkey([u8; 32]);

impl Pubkey {
    pub const fn new_from_array(pubkey: [u8; 32]) -> Self {
        Self(pubkey)
    }

    pub fn to_bytes(self) -> [u8; 32] {
        self.0
    }
}

impl AsRef<[u8]> for Pubkey {
    fn as_ref(&self) -> &[u8] {
        &self.0[..]
    }
}

impl From<[u8; 32]> for Pubkey {
    fn from(from: [u8; 32]) -> Self {
        Self(from)
    }
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct MessageAddressTableLookup {
    pub account_key: Pubkey,
    #[serde(with = "short_vec")]
    pub writable_indexes: Vec<u8>,
    #[serde(with = "short_vec")]
    pub readonly_indexes: Vec<u8>,
}
