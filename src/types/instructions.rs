use serde::{Deserialize, Serialize};
use solana_short_vec as short_vec;
use tsify::Tsify;

#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, <PERSON><PERSON>, De<PERSON>ult, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct CompiledInstruction {
    pub program_id_index: u8,
    #[serde(with = "short_vec")]
    pub accounts: Vec<u8>,
    #[serde(with = "short_vec")]
    pub data: Vec<u8>,
}
