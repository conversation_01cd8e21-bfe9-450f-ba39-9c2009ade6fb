pub mod types;

use wasm_bindgen::prelude::*;

pub use types::*;

#[wasm_bindgen]
pub fn decode_entries(slot: u64, bytes: &[u8]) -> Result<ParsedEntry, JsValue> {
    if bytes.is_empty() {
        return Err(JsValue::from_str("Empty data provided"));
    }

    let entries: Vec<types::Entry> =
        bincode::deserialize(bytes).map_err(|e| JsValue::from_str(&format!("Failed to deserialize entries: {}", e)))?;

    Ok(ParsedEntry { slot, entries })
}
