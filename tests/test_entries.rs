mod common;

use bincode;
use common::{get_edge_case_files, get_sample_files, load_all_shred_files};

use shredstream_decoder::Entry as CustomEntry;
use solana_entry::entry::Entry as OfficialEntry;

#[test]
fn test_entry_binary_compatibility_all_files() {
    let test_data = load_all_shred_files().expect("Failed to load test data");
    let mut success_count = 0;
    let mut total_entries = 0;

    for data in test_data {
        match test_single_file_entries(&data.data, &data.filename) {
            Ok(entry_count) => {
                success_count += 1;
                total_entries += entry_count;
                println!("✓ {} - {} entries", data.filename, entry_count);
            }
            Err(e) => {
                println!("✗ {} - Error: {}", data.filename, e);
            }
        }
    }

    println!("\nSummary:");
    println!("Files processed successfully: {}/100", success_count);
    println!("Total entries validated: {}", total_entries);

    assert!(success_count > 0, "No files were processed successfully");
}

#[test]
fn test_entry_binary_compatibility_edge_cases() {
    let (smallest, largest) = get_edge_case_files().expect("Failed to load edge case files");

    println!(
        "Testing smallest file: {} ({} bytes)",
        smallest.filename, smallest.size
    );
    let small_entries = test_single_file_entries(&smallest.data, &smallest.filename)
        .expect("Failed to process smallest file");

    println!(
        "Testing largest file: {} ({} bytes)",
        largest.filename, largest.size
    );
    let large_entries = test_single_file_entries(&largest.data, &largest.filename)
        .expect("Failed to process largest file");

    println!("Smallest file entries: {}", small_entries);
    println!("Largest file entries: {}", large_entries);
}

#[test]
fn test_entry_binary_compatibility_sample() {
    let samples = get_sample_files(10).expect("Failed to load sample files");

    for data in samples {
        let entry_count = test_single_file_entries(&data.data, &data.filename)
            .expect(&format!("Failed to process sample file: {}", data.filename));

        println!(
            "✓ {} - {} entries ({} bytes)",
            data.filename, entry_count, data.size
        );
    }
}

fn test_single_file_entries(
    data: &[u8],
    filename: &str,
) -> Result<usize, Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Custom deserialization failed for {}: {}", filename, e))?;

    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Official deserialization failed for {}: {}", filename, e))?;

    if custom_entries.len() != official_entries.len() {
        return Err(format!(
            "Entry count mismatch in {}: custom={}, official={}",
            filename,
            custom_entries.len(),
            official_entries.len()
        )
        .into());
    }

    for (i, (custom, official)) in custom_entries
        .iter()
        .zip(official_entries.iter())
        .enumerate()
    {
        validate_entry_equality(custom, official, filename, i)?;
    }

    Ok(custom_entries.len())
}

fn validate_entry_equality(
    custom: &CustomEntry,
    official: &OfficialEntry,
    filename: &str,
    index: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    if custom.num_hashes != official.num_hashes {
        return Err(format!(
            "num_hashes mismatch in {} entry {}: custom={}, official={}",
            filename, index, custom.num_hashes, official.num_hashes
        )
        .into());
    }

    if custom.hash.to_bytes() != official.hash.to_bytes() {
        return Err(format!("hash mismatch in {} entry {}", filename, index).into());
    }

    if custom.transactions.len() != official.transactions.len() {
        return Err(format!(
            "transaction count mismatch in {} entry {}: custom={}, official={}",
            filename,
            index,
            custom.transactions.len(),
            official.transactions.len()
        )
        .into());
    }

    let custom_serialized = bincode::serialize(custom)
        .map_err(|e| format!("Failed to serialize custom entry: {}", e))?;
    let official_serialized = bincode::serialize(official)
        .map_err(|e| format!("Failed to serialize official entry: {}", e))?;

    if custom_serialized != official_serialized {
        return Err(format!("Serialized data mismatch in {} entry {}", filename, index).into());
    }

    Ok(())
}
