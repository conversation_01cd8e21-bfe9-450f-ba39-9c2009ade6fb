mod common;

use bincode;
use common::{get_edge_case_files, get_sample_files, load_all_shred_files};
use std::collections::HashMap;

use shredstream_decoder::decode_entries;
use solana_entry::entry::Entry as OfficialEntry;

#[test]
fn test_full_pipeline_integration_all_files() {
    let test_data = load_all_shred_files().expect("Failed to load test data");
    let mut success_count = 0;
    let mut total_entries = 0;
    let mut total_transactions = 0;
    let mut error_summary = HashMap::new();

    for data in test_data {
        match test_full_pipeline(&data.data, &data.filename) {
            Ok((entries, transactions)) => {
                success_count += 1;
                total_entries += entries;
                total_transactions += transactions;
                println!(
                    "✓ {} - {} entries, {} transactions",
                    data.filename, entries, transactions
                );
            }
            Err(e) => {
                let error_type = classify_error(&e.to_string());
                *error_summary.entry(error_type).or_insert(0) += 1;
                println!("✗ {} - Error: {}", data.filename, e);
            }
        }
    }

    println!("\nIntegration Test Summary:");
    println!("Files processed successfully: {}/100", success_count);
    println!("Total entries processed: {}", total_entries);
    println!("Total transactions processed: {}", total_transactions);

    if !error_summary.is_empty() {
        println!("\nError Summary:");
        for (error_type, count) in error_summary {
            println!("  {}: {} files", error_type, count);
        }
    }

    assert!(success_count > 0, "No files were processed successfully");
    assert!(total_entries > 0, "No entries were processed");
}

#[test]
fn test_full_pipeline_edge_cases() {
    let (smallest, largest) = get_edge_case_files().expect("Failed to load edge case files");

    println!(
        "Testing smallest file: {} ({} bytes)",
        smallest.filename, smallest.size
    );
    let (small_entries, small_txs) = test_full_pipeline(&smallest.data, &smallest.filename)
        .expect("Failed to process smallest file");

    println!(
        "Testing largest file: {} ({} bytes)",
        largest.filename, largest.size
    );
    let (large_entries, large_txs) = test_full_pipeline(&largest.data, &largest.filename)
        .expect("Failed to process largest file");

    println!(
        "Smallest file: {} entries, {} transactions",
        small_entries, small_txs
    );
    println!(
        "Largest file: {} entries, {} transactions",
        large_entries, large_txs
    );

    assert!(small_entries > 0, "Smallest file should have entries");
    assert!(large_entries > 0, "Largest file should have entries");
}

#[test]
fn test_wasm_function_compatibility() {
    let samples = get_sample_files(10).expect("Failed to load sample files");

    for data in samples {
        test_wasm_decode_function(&data.data, &data.filename)
            .expect(&format!("WASM function test failed for: {}", data.filename));
    }
}

#[test]
fn test_performance_comparison() {
    let samples = get_sample_files(5).expect("Failed to load sample files");

    for data in samples {
        let (custom_time, official_time) = measure_decode_performance(&data.data)
            .expect(&format!("Performance test failed for: {}", data.filename));

        println!(
            "Performance for {}: Custom={}μs, Official={}μs, Ratio={:.2}x",
            data.filename,
            custom_time,
            official_time,
            custom_time as f64 / official_time as f64
        );
    }
}

#[test]
fn test_memory_usage_comparison() {
    let samples = get_sample_files(3).expect("Failed to load sample files");

    for data in samples {
        test_memory_usage(&data.data, &data.filename)
            .expect(&format!("Memory usage test failed for: {}", data.filename));
    }
}

fn test_full_pipeline(
    data: &[u8],
    filename: &str,
) -> Result<(usize, usize), Box<dyn std::error::Error>> {
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Official deserialization failed for {}: {}", filename, e))?;

    let custom_result = decode_entries(0, data)
        .map_err(|e| format!("Custom decode_entries failed for {}: {:?}", filename, e))?;

    if custom_result.entries.len() != official_entries.len() {
        return Err(format!(
            "Entry count mismatch in {}: custom={}, official={}",
            filename,
            custom_result.entries.len(),
            official_entries.len()
        )
        .into());
    }

    let mut total_transactions = 0;
    for (i, (custom_entry, official_entry)) in custom_result
        .entries
        .iter()
        .zip(official_entries.iter())
        .enumerate()
    {
        if custom_entry.num_hashes != official_entry.num_hashes {
            return Err(format!(
                "num_hashes mismatch in {} entry {}: custom={}, official={}",
                filename, i, custom_entry.num_hashes, official_entry.num_hashes
            )
            .into());
        }

        if custom_entry.hash.to_bytes() != official_entry.hash.to_bytes() {
            return Err(format!("hash mismatch in {} entry {}", filename, i).into());
        }

        if custom_entry.transactions.len() != official_entry.transactions.len() {
            return Err(format!(
                "transaction count mismatch in {} entry {}: custom={}, official={}",
                filename,
                i,
                custom_entry.transactions.len(),
                official_entry.transactions.len()
            )
            .into());
        }

        total_transactions += custom_entry.transactions.len();
    }

    Ok((custom_result.entries.len(), total_transactions))
}

fn test_wasm_decode_function(
    data: &[u8],
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let slot = 12345u64;
    let result = decode_entries(slot, data)
        .map_err(|e| format!("WASM decode function failed for {}: {:?}", filename, e))?;

    if result.slot != slot {
        return Err(format!(
            "Slot mismatch in {}: expected={}, got={}",
            filename, slot, result.slot
        )
        .into());
    }

    if result.entries.is_empty() {
        return Err(format!("No entries returned for {}", filename).into());
    }

    Ok(())
}

fn measure_decode_performance(data: &[u8]) -> Result<(u128, u128), Box<dyn std::error::Error>> {
    use std::time::Instant;

    let start = Instant::now();
    let _custom_result =
        decode_entries(0, data).map_err(|e| format!("WASM decode error: {:?}", e))?;
    let custom_time = start.elapsed().as_micros();

    let start = Instant::now();
    let _official_result: Vec<OfficialEntry> = bincode::deserialize(data)?;
    let official_time = start.elapsed().as_micros();

    Ok((custom_time, official_time))
}

fn test_memory_usage(data: &[u8], filename: &str) -> Result<(), Box<dyn std::error::Error>> {
    let custom_result =
        decode_entries(0, data).map_err(|e| format!("WASM decode error: {:?}", e))?;
    let official_result: Vec<OfficialEntry> = bincode::deserialize(data)?;

    let custom_serialized = bincode::serialize(&custom_result)?;
    let official_serialized = bincode::serialize(&official_result)?;

    println!(
        "Memory usage for {}: Custom={}B, Official={}B",
        filename,
        custom_serialized.len(),
        official_serialized.len()
    );

    Ok(())
}

fn classify_error(error: &str) -> String {
    if error.contains("deserialization") {
        "Deserialization Error".to_string()
    } else if error.contains("mismatch") {
        "Data Mismatch".to_string()
    } else if error.contains("decode_entries") {
        "WASM Function Error".to_string()
    } else {
        "Other Error".to_string()
    }
}
