use std::fs;
use std::path::Path;

const SHREDS_DIR: &str = "test/raw_shreds";

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Analyzing collected shred data...");
    println!();

    let mut total_size = 0u64;
    let mut min_size = u64::MAX;
    let mut max_size = 0u64;
    let mut file_count = 0;

    for i in 1..=100 {
        let filename = format!("shred_{:03}.bin", i);
        let filepath = Path::new(SHREDS_DIR).join(&filename);
        
        if filepath.exists() {
            let metadata = fs::metadata(&filepath)?;
            let size = metadata.len();
            
            total_size += size;
            min_size = min_size.min(size);
            max_size = max_size.max(size);
            file_count += 1;
            
            if i <= 10 || i % 10 == 0 {
                println!("{}: {} bytes", filename, size);
            }
        }
    }

    println!();
    println!("=== Analysis Summary ===");
    println!("Total files: {}", file_count);
    println!("Total size: {} bytes ({:.2} MB)", total_size, total_size as f64 / 1_048_576.0);
    println!("Average size: {} bytes", total_size / file_count as u64);
    println!("Min size: {} bytes", min_size);
    println!("Max size: {} bytes", max_size);
    println!("Size range: {} bytes", max_size - min_size);

    Ok(())
}
