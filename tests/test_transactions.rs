mod common;

use bincode;
use common::{get_sample_files, load_all_shred_files};

use shredstream_decoder::{Entry as CustomEntry, VersionedTransaction as CustomTransaction};
use solana_entry::entry::Entry as OfficialEntry;
use solana_transaction::versioned::VersionedTransaction as OfficialTransaction;

#[test]
fn test_transaction_binary_compatibility_all_files() {
    let test_data = load_all_shred_files().expect("Failed to load test data");
    let mut success_count = 0;
    let mut total_transactions = 0;

    for data in test_data {
        match test_single_file_transactions(&data.data, &data.filename) {
            Ok(tx_count) => {
                success_count += 1;
                total_transactions += tx_count;
                if tx_count > 0 {
                    println!("✓ {} - {} transactions", data.filename, tx_count);
                }
            }
            Err(e) => {
                println!("✗ {} - Error: {}", data.filename, e);
            }
        }
    }

    println!("\nTransaction Test Summary:");
    println!("Files processed successfully: {}/100", success_count);
    println!("Total transactions validated: {}", total_transactions);

    assert!(success_count > 0, "No files were processed successfully");
    assert!(total_transactions > 0, "No transactions found in any file");
}

#[test]
fn test_transaction_binary_compatibility_sample() {
    let samples = get_sample_files(10).expect("Failed to load sample files");

    for data in samples {
        let tx_count = test_single_file_transactions(&data.data, &data.filename)
            .expect(&format!("Failed to process sample file: {}", data.filename));

        if tx_count > 0 {
            println!(
                "✓ {} - {} transactions ({} bytes)",
                data.filename, tx_count, data.size
            );
        }
    }
}

#[test]
fn test_transaction_field_by_field_validation() {
    let samples = get_sample_files(5).expect("Failed to load sample files");

    for data in samples {
        validate_transaction_fields(&data.data, &data.filename)
            .expect(&format!("Field validation failed for: {}", data.filename));
    }
}

fn test_single_file_transactions(
    data: &[u8],
    filename: &str,
) -> Result<usize, Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Custom deserialization failed for {}: {}", filename, e))?;

    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Official deserialization failed for {}: {}", filename, e))?;

    let mut total_transactions = 0;

    for (entry_idx, (custom_entry, official_entry)) in custom_entries
        .iter()
        .zip(official_entries.iter())
        .enumerate()
    {
        if custom_entry.transactions.len() != official_entry.transactions.len() {
            return Err(format!(
                "Transaction count mismatch in {} entry {}: custom={}, official={}",
                filename,
                entry_idx,
                custom_entry.transactions.len(),
                official_entry.transactions.len()
            )
            .into());
        }

        for (tx_idx, (custom_tx, official_tx)) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
            .enumerate()
        {
            validate_transaction_equality(custom_tx, official_tx, filename, entry_idx, tx_idx)?;
            total_transactions += 1;
        }
    }

    Ok(total_transactions)
}

fn validate_transaction_fields(
    data: &[u8],
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            if custom_tx.signatures.len() != official_tx.signatures.len() {
                return Err(format!(
                    "Signature count mismatch in {}: custom={}, official={}",
                    filename,
                    custom_tx.signatures.len(),
                    official_tx.signatures.len()
                )
                .into());
            }

            for (custom_sig, official_sig) in custom_tx
                .signatures
                .iter()
                .zip(official_tx.signatures.iter())
            {
                if custom_sig.as_ref() != official_sig.as_ref() {
                    return Err(format!("Signature mismatch in {}", filename).into());
                }
            }
        }
    }

    Ok(())
}

fn validate_transaction_equality(
    custom: &CustomTransaction,
    official: &OfficialTransaction,
    filename: &str,
    entry_idx: usize,
    tx_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    if custom.signatures.len() != official.signatures.len() {
        return Err(format!(
            "Signature count mismatch in {} entry {} tx {}: custom={}, official={}",
            filename,
            entry_idx,
            tx_idx,
            custom.signatures.len(),
            official.signatures.len()
        )
        .into());
    }

    for (i, (custom_sig, official_sig)) in custom
        .signatures
        .iter()
        .zip(official.signatures.iter())
        .enumerate()
    {
        if custom_sig.as_ref() != official_sig.as_ref() {
            return Err(format!(
                "Signature {} mismatch in {} entry {} tx {}",
                i, filename, entry_idx, tx_idx
            )
            .into());
        }
    }

    let custom_serialized = bincode::serialize(custom)
        .map_err(|e| format!("Failed to serialize custom transaction: {}", e))?;
    let official_serialized = bincode::serialize(official)
        .map_err(|e| format!("Failed to serialize official transaction: {}", e))?;

    if custom_serialized != official_serialized {
        return Err(format!(
            "Serialized transaction data mismatch in {} entry {} tx {}",
            filename, entry_idx, tx_idx
        )
        .into());
    }

    Ok(())
}
