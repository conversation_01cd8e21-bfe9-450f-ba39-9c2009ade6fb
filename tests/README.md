# Comprehensive Solana Data Structure Tests

This test suite validates the 100% binary compatibility of our custom WASM-compatible Solana data structures against the official `solana-entry` crate.

## Test Overview

### 🎯 Goal

Prove that our custom types in `src/types/` are functionally equivalent to official Solana types for all real-world data patterns.

### 📊 Test Data

-   **1000 raw shred samples** collected from live Solana network
-   **Real-world data patterns** from slots *********-*********
-   **Size range**: 56 - 59,660 bytes per file
-   **Total data**: ~30 MB of binary shred data
-   **Coverage**: 30,893 entries and 50,120 transactions

## Test Structure

### Module Tests

-   **`test_entries.rs`** - Entry struct compatibility
-   **`test_transactions.rs`** - VersionedTransaction compatibility
-   **`test_messages.rs`** - VersionedMessage, LegacyMessage, V0Message compatibility
-   **`test_instructions.rs`** - CompiledInstruction compatibility
-   **`test_accounts.rs`** - <PERSON>key and MessageAddressTableLookup compatibility

### Integration Tests

-   **`test_integration.rs`** - Full pipeline testing and performance comparison

### Helper Module

-   **`common/mod.rs`** - Utilities for loading and managing test data

## Test Categories

### 1. Binary Compatibility Testing

-   Deserialize identical binary data with both custom and official types
-   Verify byte-for-byte equality of serialized results
-   Test all 100 collected shred files

### 2. Field-by-Field Validation

-   Compare individual struct fields
-   Validate data integrity across type conversions
-   Ensure no data loss or corruption

### 3. Edge Case Testing

-   Test smallest and largest files
-   Handle various transaction types and patterns
-   Validate error handling consistency

### 4. Integration Testing

-   Test complete decode pipeline
-   WASM function compatibility
-   Performance comparison
-   Memory usage analysis

## Running Tests

### Quick Test Run

```bash
# Run all tests
cargo test

# Run specific module tests
cargo test test_entries
cargo test test_transactions
cargo test test_messages
cargo test test_instructions
cargo test test_accounts
cargo test test_integration
```

### Comprehensive Test Suite

```bash
# Run the full test suite with detailed output
./run_tests.sh
```

### Prerequisites

```bash
# Ensure test data is available
./collect_shreds.sh  # If not already collected
```

## Test Validation Criteria

### ✅ Success Criteria

-   **100% binary compatibility** - All deserialized data must match exactly
-   **Field equality** - Every struct field must be identical
-   **Serialization consistency** - Re-serialized data must be byte-identical
-   **Performance parity** - Custom types should perform comparably to official types

### 🔍 What We Test

-   **Entry structures** - num_hashes, hash, transactions
-   **Transaction data** - signatures, versioned messages
-   **Message formats** - Legacy vs V0, headers, account keys, instructions
-   **Instruction details** - program_id_index, accounts, data
-   **Account information** - Pubkey representation, address table lookups
-   **Hash consistency** - Solana hash compatibility
-   **Signature validation** - Signature byte representation

## Test Results Interpretation

### Success Indicators

-   ✅ All files processed successfully
-   ✅ Entry/transaction/instruction counts match
-   ✅ Binary serialization identical
-   ✅ No field mismatches

### Failure Analysis

-   ❌ Deserialization errors indicate type incompatibility
-   ❌ Field mismatches suggest struct definition issues
-   ❌ Serialization differences indicate encoding problems
-   ❌ Count mismatches suggest parsing errors

## Continuous Validation

These tests serve as:

-   **Regression prevention** - Ensure changes don't break compatibility
-   **Confidence building** - Prove reliability for production use
-   **Documentation** - Demonstrate correct usage patterns
-   **Performance monitoring** - Track performance characteristics

## Adding New Tests

When adding new custom types:

1. Create corresponding test file in `tests/`
2. Follow existing patterns for binary compatibility testing
3. Add field-by-field validation
4. Include edge case testing
5. Update integration tests if needed

The goal is maintaining 100% compatibility with official Solana types while providing WASM-friendly interfaces.
