mod common;

use bincode;
use common::{get_sample_files, load_all_shred_files};

use shredstream_decoder::{
    CompiledInstruction as CustomInstruction, Entry as CustomEntry,
    VersionedMessage as CustomMessage,
};
use solana_entry::entry::Entry as OfficialEntry;
use solana_message::{
    compiled_instruction::CompiledInstruction as OfficialInstruction,
    VersionedMessage as OfficialMessage,
};

#[test]
fn test_instruction_binary_compatibility_all_files() {
    let test_data = load_all_shred_files().expect("Failed to load test data");
    let mut success_count = 0;
    let mut total_instructions = 0;

    for data in test_data {
        match test_single_file_instructions(&data.data, &data.filename) {
            Ok(instruction_count) => {
                success_count += 1;
                total_instructions += instruction_count;
                if instruction_count > 0 {
                    println!("✓ {} - {} instructions", data.filename, instruction_count);
                }
            }
            Err(e) => {
                println!("✗ {} - Error: {}", data.filename, e);
            }
        }
    }

    println!("\nInstruction Test Summary:");
    println!("Files processed successfully: {}/100", success_count);
    println!("Total instructions validated: {}", total_instructions);

    assert!(success_count > 0, "No files were processed successfully");
    assert!(total_instructions > 0, "No instructions found in any file");
}

#[test]
fn test_instruction_field_validation() {
    let samples = get_sample_files(10).expect("Failed to load sample files");

    for data in samples {
        validate_instruction_fields(&data.data, &data.filename).expect(&format!(
            "Instruction field validation failed for: {}",
            data.filename
        ));
    }
}

#[test]
fn test_instruction_data_integrity() {
    let samples = get_sample_files(5).expect("Failed to load sample files");

    for data in samples {
        validate_instruction_data(&data.data, &data.filename).expect(&format!(
            "Instruction data validation failed for: {}",
            data.filename
        ));
    }
}

#[test]
fn test_instruction_accounts_integrity() {
    let samples = get_sample_files(5).expect("Failed to load sample files");

    for data in samples {
        validate_instruction_accounts(&data.data, &data.filename).expect(&format!(
            "Instruction accounts validation failed for: {}",
            data.filename
        ));
    }
}

fn test_single_file_instructions(
    data: &[u8],
    filename: &str,
) -> Result<usize, Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Custom deserialization failed for {}: {}", filename, e))?;

    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Official deserialization failed for {}: {}", filename, e))?;

    let mut total_instructions = 0;

    for (entry_idx, (custom_entry, official_entry)) in custom_entries
        .iter()
        .zip(official_entries.iter())
        .enumerate()
    {
        for (tx_idx, (custom_tx, official_tx)) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
            .enumerate()
        {
            let (custom_instructions, official_instructions) =
                extract_instructions(&custom_tx.message, &official_tx.message)?;

            if custom_instructions.len() != official_instructions.len() {
                return Err(format!(
                    "Instruction count mismatch in {} entry {} tx {}: custom={}, official={}",
                    filename,
                    entry_idx,
                    tx_idx,
                    custom_instructions.len(),
                    official_instructions.len()
                )
                .into());
            }

            for (inst_idx, (custom_inst, official_inst)) in custom_instructions
                .iter()
                .zip(official_instructions.iter())
                .enumerate()
            {
                validate_instruction_equality(
                    custom_inst,
                    official_inst,
                    filename,
                    entry_idx,
                    tx_idx,
                    inst_idx,
                )?;
                total_instructions += 1;
            }
        }
    }

    Ok(total_instructions)
}

fn validate_instruction_fields(
    data: &[u8],
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            let (custom_instructions, official_instructions) =
                extract_instructions(&custom_tx.message, &official_tx.message)?;

            for (custom_inst, official_inst) in
                custom_instructions.iter().zip(official_instructions.iter())
            {
                if custom_inst.program_id_index != official_inst.program_id_index {
                    return Err(format!(
                        "program_id_index mismatch in {}: custom={}, official={}",
                        filename, custom_inst.program_id_index, official_inst.program_id_index
                    )
                    .into());
                }

                if custom_inst.accounts.len() != official_inst.accounts.len() {
                    return Err(format!(
                        "accounts length mismatch in {}: custom={}, official={}",
                        filename,
                        custom_inst.accounts.len(),
                        official_inst.accounts.len()
                    )
                    .into());
                }

                if custom_inst.data.len() != official_inst.data.len() {
                    return Err(format!(
                        "data length mismatch in {}: custom={}, official={}",
                        filename,
                        custom_inst.data.len(),
                        official_inst.data.len()
                    )
                    .into());
                }
            }
        }
    }

    Ok(())
}

fn validate_instruction_data(
    data: &[u8],
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            let (custom_instructions, official_instructions) =
                extract_instructions(&custom_tx.message, &official_tx.message)?;

            for (custom_inst, official_inst) in
                custom_instructions.iter().zip(official_instructions.iter())
            {
                if custom_inst.data != official_inst.data {
                    return Err(format!("Instruction data mismatch in {}", filename).into());
                }
            }
        }
    }

    Ok(())
}

fn validate_instruction_accounts(
    data: &[u8],
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            let (custom_instructions, official_instructions) =
                extract_instructions(&custom_tx.message, &official_tx.message)?;

            for (custom_inst, official_inst) in
                custom_instructions.iter().zip(official_instructions.iter())
            {
                if custom_inst.accounts != official_inst.accounts {
                    return Err(format!("Instruction accounts mismatch in {}", filename).into());
                }
            }
        }
    }

    Ok(())
}

fn extract_instructions<'a>(
    custom_msg: &'a CustomMessage,
    official_msg: &'a OfficialMessage,
) -> Result<(&'a Vec<CustomInstruction>, &'a Vec<OfficialInstruction>), Box<dyn std::error::Error>>
{
    match (custom_msg, official_msg) {
        (CustomMessage::Legacy(custom), OfficialMessage::Legacy(official)) => {
            Ok((&custom.instructions, &official.instructions))
        }
        (CustomMessage::V0(custom), OfficialMessage::V0(official)) => {
            Ok((&custom.instructions, &official.instructions))
        }
        _ => Err("Message type mismatch".into()),
    }
}

fn validate_instruction_equality(
    custom: &CustomInstruction,
    official: &OfficialInstruction,
    filename: &str,
    entry_idx: usize,
    tx_idx: usize,
    inst_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    if custom.program_id_index != official.program_id_index {
        return Err(format!(
            "program_id_index mismatch in {} entry {} tx {} instruction {}: custom={}, official={}",
            filename,
            entry_idx,
            tx_idx,
            inst_idx,
            custom.program_id_index,
            official.program_id_index
        )
        .into());
    }

    if custom.accounts != official.accounts {
        return Err(format!(
            "accounts mismatch in {} entry {} tx {} instruction {}",
            filename, entry_idx, tx_idx, inst_idx
        )
        .into());
    }

    if custom.data != official.data {
        return Err(format!(
            "data mismatch in {} entry {} tx {} instruction {}",
            filename, entry_idx, tx_idx, inst_idx
        )
        .into());
    }

    let custom_serialized = bincode::serialize(custom)
        .map_err(|e| format!("Failed to serialize custom instruction: {}", e))?;
    let official_serialized = bincode::serialize(official)
        .map_err(|e| format!("Failed to serialize official instruction: {}", e))?;

    if custom_serialized != official_serialized {
        return Err(format!(
            "Serialized instruction data mismatch in {} entry {} tx {} instruction {}",
            filename, entry_idx, tx_idx, inst_idx
        )
        .into());
    }

    Ok(())
}
