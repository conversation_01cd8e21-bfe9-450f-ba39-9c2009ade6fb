mod common;

use bincode;
use common::{get_sample_files, load_all_shred_files};

use shredstream_decoder::{Entry as CustomEntry, VersionedMessage as CustomMessage};
use solana_entry::entry::Entry as OfficialEntry;
use solana_message::VersionedMessage as OfficialMessage;

#[test]
fn test_message_binary_compatibility_all_files() {
    let test_data = load_all_shred_files().expect("Failed to load test data");
    let mut success_count = 0;
    let mut total_messages = 0;
    let mut legacy_count = 0;
    let mut v0_count = 0;

    for data in test_data {
        match test_single_file_messages(&data.data, &data.filename) {
            Ok((msg_count, legacy, v0)) => {
                success_count += 1;
                total_messages += msg_count;
                legacy_count += legacy;
                v0_count += v0;
                if msg_count > 0 {
                    println!(
                        "✓ {} - {} messages (Legacy: {}, V0: {})",
                        data.filename, msg_count, legacy, v0
                    );
                }
            }
            Err(e) => {
                println!("✗ {} - Error: {}", data.filename, e);
            }
        }
    }

    println!("\nMessage Test Summary:");
    println!("Files processed successfully: {}/100", success_count);
    println!("Total messages validated: {}", total_messages);
    println!("Legacy messages: {}", legacy_count);
    println!("V0 messages: {}", v0_count);

    assert!(success_count > 0, "No files were processed successfully");
    assert!(total_messages > 0, "No messages found in any file");
}

#[test]
fn test_message_header_compatibility() {
    let samples = get_sample_files(10).expect("Failed to load sample files");

    for data in samples {
        validate_message_headers(&data.data, &data.filename).expect(&format!(
            "Message header validation failed for: {}",
            data.filename
        ));
    }
}

#[test]
fn test_message_account_keys_compatibility() {
    let samples = get_sample_files(5).expect("Failed to load sample files");

    for data in samples {
        validate_message_account_keys(&data.data, &data.filename).expect(&format!(
            "Account keys validation failed for: {}",
            data.filename
        ));
    }
}

#[test]
fn test_message_instructions_compatibility() {
    let samples = get_sample_files(5).expect("Failed to load sample files");

    for data in samples {
        validate_message_instructions(&data.data, &data.filename).expect(&format!(
            "Instructions validation failed for: {}",
            data.filename
        ));
    }
}

fn test_single_file_messages(
    data: &[u8],
    filename: &str,
) -> Result<(usize, usize, usize), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Custom deserialization failed for {}: {}", filename, e))?;

    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Official deserialization failed for {}: {}", filename, e))?;

    let mut total_messages = 0;
    let mut legacy_count = 0;
    let mut v0_count = 0;

    for (entry_idx, (custom_entry, official_entry)) in custom_entries
        .iter()
        .zip(official_entries.iter())
        .enumerate()
    {
        for (tx_idx, (custom_tx, official_tx)) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
            .enumerate()
        {
            validate_message_equality(
                &custom_tx.message,
                &official_tx.message,
                filename,
                entry_idx,
                tx_idx,
            )?;

            total_messages += 1;
            match &custom_tx.message {
                CustomMessage::Legacy(_) => legacy_count += 1,
                CustomMessage::V0(_) => v0_count += 1,
            }
        }
    }

    Ok((total_messages, legacy_count, v0_count))
}

fn validate_message_headers(data: &[u8], filename: &str) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            let (custom_header, official_header) = match (&custom_tx.message, &official_tx.message)
            {
                (CustomMessage::Legacy(custom_msg), OfficialMessage::Legacy(official_msg)) => {
                    (&custom_msg.header, &official_msg.header)
                }
                (CustomMessage::V0(custom_msg), OfficialMessage::V0(official_msg)) => {
                    (&custom_msg.header, &official_msg.header)
                }
                _ => return Err(format!("Message type mismatch in {}", filename).into()),
            };

            if custom_header.num_required_signatures != official_header.num_required_signatures
                || custom_header.num_readonly_signed_accounts
                    != official_header.num_readonly_signed_accounts
                || custom_header.num_readonly_unsigned_accounts
                    != official_header.num_readonly_unsigned_accounts
            {
                return Err(format!("Message header mismatch in {}", filename).into());
            }
        }
    }

    Ok(())
}

fn validate_message_account_keys(
    data: &[u8],
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            let (custom_keys, official_keys) = match (&custom_tx.message, &official_tx.message) {
                (CustomMessage::Legacy(custom_msg), OfficialMessage::Legacy(official_msg)) => {
                    (&custom_msg.account_keys, &official_msg.account_keys)
                }
                (CustomMessage::V0(custom_msg), OfficialMessage::V0(official_msg)) => {
                    (&custom_msg.account_keys, &official_msg.account_keys)
                }
                _ => return Err(format!("Message type mismatch in {}", filename).into()),
            };

            if custom_keys.len() != official_keys.len() {
                return Err(format!("Account keys count mismatch in {}", filename).into());
            }

            for (custom_key, official_key) in custom_keys.iter().zip(official_keys.iter()) {
                if custom_key.as_ref() != official_key.as_ref() {
                    return Err(format!("Account key mismatch in {}", filename).into());
                }
            }
        }
    }

    Ok(())
}

fn validate_message_instructions(
    data: &[u8],
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            let (custom_instructions, official_instructions) =
                match (&custom_tx.message, &official_tx.message) {
                    (CustomMessage::Legacy(custom_msg), OfficialMessage::Legacy(official_msg)) => {
                        (&custom_msg.instructions, &official_msg.instructions)
                    }
                    (CustomMessage::V0(custom_msg), OfficialMessage::V0(official_msg)) => {
                        (&custom_msg.instructions, &official_msg.instructions)
                    }
                    _ => return Err(format!("Message type mismatch in {}", filename).into()),
                };

            if custom_instructions.len() != official_instructions.len() {
                return Err(format!("Instructions count mismatch in {}", filename).into());
            }
        }
    }

    Ok(())
}

fn validate_message_equality(
    custom: &CustomMessage,
    official: &OfficialMessage,
    filename: &str,
    entry_idx: usize,
    tx_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    let custom_serialized = bincode::serialize(custom)
        .map_err(|e| format!("Failed to serialize custom message: {}", e))?;
    let official_serialized = bincode::serialize(official)
        .map_err(|e| format!("Failed to serialize official message: {}", e))?;

    if custom_serialized != official_serialized {
        return Err(format!(
            "Serialized message data mismatch in {} entry {} tx {}",
            filename, entry_idx, tx_idx
        )
        .into());
    }

    Ok(())
}
