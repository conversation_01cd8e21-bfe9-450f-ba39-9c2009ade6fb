use std::fs;
use std::path::Path;

pub const SHREDS_DIR: &str = "test/raw_shreds";
pub const TOTAL_SHRED_FILES: usize = 1000;

pub struct TestData {
    pub filename: String,
    pub data: Vec<u8>,
    pub size: usize,
}

pub fn load_shred_file(index: usize) -> Result<TestData, Box<dyn std::error::Error>> {
    if index == 0 || index > TOTAL_SHRED_FILES {
        return Err(format!(
            "Invalid shred index: {}. Must be 1-{}",
            index, TOTAL_SHRED_FILES
        )
        .into());
    }

    let filename = format!("shred_{:03}.bin", index);
    let filepath = Path::new(SHREDS_DIR).join(&filename);

    let data = fs::read(&filepath)?;
    let size = data.len();

    Ok(TestData {
        filename,
        data,
        size,
    })
}

pub fn load_all_shred_files() -> Result<Vec<TestData>, Box<dyn std::error::Error>> {
    let mut test_data = Vec::new();

    for i in 1..=TOTAL_SHRED_FILES {
        match load_shred_file(i) {
            Ok(data) => test_data.push(data),
            Err(e) => eprintln!(
                "Warning: Failed to load {}: {}",
                format!("shred_{:03}.bin", i),
                e
            ),
        }
    }

    if test_data.is_empty() {
        return Err("No test data files found".into());
    }

    Ok(test_data)
}

pub fn get_edge_case_files() -> Result<(TestData, TestData), Box<dyn std::error::Error>> {
    let all_data = load_all_shred_files()?;

    let smallest = all_data
        .iter()
        .min_by_key(|data| data.size)
        .ok_or("No files found")?;

    let largest = all_data
        .iter()
        .max_by_key(|data| data.size)
        .ok_or("No files found")?;

    Ok((
        TestData {
            filename: smallest.filename.clone(),
            data: smallest.data.clone(),
            size: smallest.size,
        },
        TestData {
            filename: largest.filename.clone(),
            data: largest.data.clone(),
            size: largest.size,
        },
    ))
}

pub fn get_sample_files(count: usize) -> Result<Vec<TestData>, Box<dyn std::error::Error>> {
    let all_data = load_all_shred_files()?;

    if count >= all_data.len() {
        return Ok(all_data);
    }

    let step = all_data.len() / count;
    let mut samples = Vec::new();

    for i in 0..count {
        let index = i * step;
        if index < all_data.len() {
            samples.push(TestData {
                filename: all_data[index].filename.clone(),
                data: all_data[index].data.clone(),
                size: all_data[index].size,
            });
        }
    }

    Ok(samples)
}
