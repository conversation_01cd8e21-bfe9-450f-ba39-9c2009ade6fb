use bincode;
use std::fs;

#[test]
fn test_basic_functionality() {
    println!("Testing basic functionality...");

    let test_data_dir = "test/raw_shreds";

    if !std::path::Path::new(test_data_dir).exists() {
        println!("Test data directory not found: {}", test_data_dir);
        return;
    }

    let files = fs::read_dir(test_data_dir).expect("Failed to read test data directory");
    let mut count = 0;

    for file in files {
        if let Ok(entry) = file {
            if entry.path().extension().and_then(|s| s.to_str()) == Some("bin") {
                count += 1;
            }
        }
    }

    println!("Found {} test files", count);
    assert!(count > 0, "No test files found");
}

#[test]
fn test_solana_entry_types() {
    use solana_entry::entry::Entry as OfficialEntry;

    let test_file = "test/raw_shreds/shred_001.bin";
    if !std::path::Path::new(test_file).exists() {
        println!("Test file not found: {}", test_file);
        return;
    }

    let data = fs::read(test_file).expect("Failed to read test file");
    let entries: Result<Vec<OfficialEntry>, _> = bincode::deserialize(&data);

    match entries {
        Ok(entries) => {
            println!("Successfully deserialized {} entries", entries.len());
            if let Some(first_entry) = entries.first() {
                println!(
                    "First entry has {} transactions",
                    first_entry.transactions.len()
                );
            }
        }
        Err(e) => {
            println!("Failed to deserialize: {}", e);
        }
    }
}

#[test]
fn test_debug_deserialization() {
    use solana_entry::entry::Entry as OfficialEntry;

    let test_file = "test/raw_shreds/shred_001.bin";
    if !std::path::Path::new(test_file).exists() {
        println!("Test file not found: {}", test_file);
        return;
    }

    let data = fs::read(test_file).expect("Failed to read test file");
    println!("Data size: {} bytes", data.len());

    // First try official deserialization
    let official_entries: Result<Vec<OfficialEntry>, _> = bincode::deserialize(&data);
    match official_entries {
        Ok(entries) => {
            println!(
                "✅ Official deserialization successful: {} entries",
                entries.len()
            );
            if let Some(first_entry) = entries.first() {
                println!(
                    "First entry: {} hashes, {} transactions",
                    first_entry.num_hashes,
                    first_entry.transactions.len()
                );

                if let Some(first_tx) = first_entry.transactions.first() {
                    println!(
                        "First transaction: {} signatures",
                        first_tx.signatures.len()
                    );
                    println!(
                        "Message type: {:?}",
                        std::mem::discriminant(&first_tx.message)
                    );
                }
            }
        }
        Err(e) => {
            println!("❌ Official deserialization failed: {}", e);
            return;
        }
    }

    // Now try custom deserialization
    println!("\nTrying custom deserialization...");
    let custom_result: Result<Vec<shredstream_decoder::Entry>, _> = bincode::deserialize(&data);
    match custom_result {
        Ok(entries) => {
            println!(
                "✅ Custom deserialization successful: {} entries",
                entries.len()
            );
        }
        Err(e) => {
            println!("❌ Custom deserialization failed: {}", e);

            // Try to debug by looking at the first few bytes
            println!("First 32 bytes: {:?}", &data[..32.min(data.len())]);
        }
    }
}

#[test]
fn test_basic_compatibility() {
    use shredstream_decoder::Entry as CustomEntry;
    use solana_entry::entry::Entry as OfficialEntry;

    let test_file = "test/raw_shreds/shred_001.bin";
    if !std::path::Path::new(test_file).exists() {
        println!("Test file not found: {}", test_file);
        return;
    }

    let data = fs::read(test_file).expect("Failed to read test file");

    let custom_entries: Result<Vec<CustomEntry>, _> = bincode::deserialize(&data);
    let official_entries: Result<Vec<OfficialEntry>, _> = bincode::deserialize(&data);

    match (custom_entries, official_entries) {
        (Ok(custom), Ok(official)) => {
            println!("✅ Both deserializations successful!");
            println!("Custom entries: {}", custom.len());
            println!("Official entries: {}", official.len());

            assert_eq!(custom.len(), official.len(), "Entry count mismatch");

            for (i, (c, o)) in custom.iter().zip(official.iter()).enumerate() {
                assert_eq!(
                    c.num_hashes, o.num_hashes,
                    "num_hashes mismatch at entry {}",
                    i
                );
                assert_eq!(
                    c.hash.to_bytes(),
                    o.hash.to_bytes(),
                    "hash mismatch at entry {}",
                    i
                );
                assert_eq!(
                    c.transactions.len(),
                    o.transactions.len(),
                    "transaction count mismatch at entry {}",
                    i
                );

                println!(
                    "Entry {}: {} hashes, {} transactions",
                    i,
                    c.num_hashes,
                    c.transactions.len()
                );
            }

            println!("✅ Basic compatibility test PASSED!");
        }
        (Err(e), _) => panic!("Custom deserialization failed: {}", e),
        (_, Err(e)) => panic!("Official deserialization failed: {}", e),
    }
}
