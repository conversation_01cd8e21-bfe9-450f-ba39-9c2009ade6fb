mod common;

use bincode;
use common::{get_sample_files, load_all_shred_files};

use shredstream_decoder::{
    Entry as CustomEntry, MessageAddressTableLookup as CustomLookup, Pubkey as CustomPubkey,
    VersionedMessage as CustomMessage,
};
use solana_entry::entry::Entry as OfficialEntry;
use solana_message::{
    v0::MessageAddressTableLookup as OfficialLookup, VersionedMessage as OfficialMessage,
};
use solana_pubkey::Pubkey as OfficialPubkey;

#[test]
fn test_pubkey_binary_compatibility_all_files() {
    let test_data = load_all_shred_files().expect("Failed to load test data");
    let mut success_count = 0;
    let mut total_pubkeys = 0;

    for data in test_data {
        match test_single_file_pubkeys(&data.data, &data.filename) {
            Ok(pubkey_count) => {
                success_count += 1;
                total_pubkeys += pubkey_count;
                if pubkey_count > 0 {
                    println!("✓ {} - {} pubkeys", data.filename, pubkey_count);
                }
            }
            Err(e) => {
                println!("✗ {} - Error: {}", data.filename, e);
            }
        }
    }

    println!("\nPubkey Test Summary:");
    println!("Files processed successfully: {}/100", success_count);
    println!("Total pubkeys validated: {}", total_pubkeys);

    assert!(success_count > 0, "No files were processed successfully");
    assert!(total_pubkeys > 0, "No pubkeys found in any file");
}

#[test]
fn test_address_table_lookup_compatibility() {
    let test_data = load_all_shred_files().expect("Failed to load test data");
    let mut success_count = 0;
    let mut total_lookups = 0;

    for data in test_data {
        match test_single_file_address_lookups(&data.data, &data.filename) {
            Ok(lookup_count) => {
                success_count += 1;
                total_lookups += lookup_count;
                if lookup_count > 0 {
                    println!(
                        "✓ {} - {} address table lookups",
                        data.filename, lookup_count
                    );
                }
            }
            Err(e) => {
                println!("✗ {} - Error: {}", data.filename, e);
            }
        }
    }

    println!("\nAddress Table Lookup Test Summary:");
    println!("Files processed successfully: {}/100", success_count);
    println!("Total address table lookups validated: {}", total_lookups);

    assert!(success_count > 0, "No files were processed successfully");
}

#[test]
fn test_pubkey_serialization_consistency() {
    let samples = get_sample_files(10).expect("Failed to load sample files");

    for data in samples {
        validate_pubkey_serialization(&data.data, &data.filename).expect(&format!(
            "Pubkey serialization validation failed for: {}",
            data.filename
        ));
    }
}

#[test]
fn test_pubkey_byte_representation() {
    let samples = get_sample_files(5).expect("Failed to load sample files");

    for data in samples {
        validate_pubkey_bytes(&data.data, &data.filename).expect(&format!(
            "Pubkey byte validation failed for: {}",
            data.filename
        ));
    }
}

fn test_single_file_pubkeys(
    data: &[u8],
    filename: &str,
) -> Result<usize, Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Custom deserialization failed for {}: {}", filename, e))?;

    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Official deserialization failed for {}: {}", filename, e))?;

    let mut total_pubkeys = 0;

    for (entry_idx, (custom_entry, official_entry)) in custom_entries
        .iter()
        .zip(official_entries.iter())
        .enumerate()
    {
        for (tx_idx, (custom_tx, official_tx)) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
            .enumerate()
        {
            let (custom_keys, official_keys) =
                extract_account_keys(&custom_tx.message, &official_tx.message)?;

            if custom_keys.len() != official_keys.len() {
                return Err(format!(
                    "Account keys count mismatch in {} entry {} tx {}: custom={}, official={}",
                    filename,
                    entry_idx,
                    tx_idx,
                    custom_keys.len(),
                    official_keys.len()
                )
                .into());
            }

            for (key_idx, (custom_key, official_key)) in
                custom_keys.iter().zip(official_keys.iter()).enumerate()
            {
                validate_pubkey_equality(
                    custom_key,
                    official_key,
                    filename,
                    entry_idx,
                    tx_idx,
                    key_idx,
                )?;
                total_pubkeys += 1;
            }
        }
    }

    Ok(total_pubkeys)
}

fn test_single_file_address_lookups(
    data: &[u8],
    filename: &str,
) -> Result<usize, Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Custom deserialization failed for {}: {}", filename, e))?;

    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)
        .map_err(|e| format!("Official deserialization failed for {}: {}", filename, e))?;

    let mut total_lookups = 0;

    for (entry_idx, (custom_entry, official_entry)) in custom_entries
        .iter()
        .zip(official_entries.iter())
        .enumerate()
    {
        for (tx_idx, (custom_tx, official_tx)) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
            .enumerate()
        {
            if let (Some(custom_lookups), Some(official_lookups)) =
                extract_address_lookups(&custom_tx.message, &official_tx.message)?
            {
                if custom_lookups.len() != official_lookups.len() {
                    return Err(format!(
                        "Address table lookups count mismatch in {} entry {} tx {}: custom={}, official={}",
                        filename, entry_idx, tx_idx, custom_lookups.len(), official_lookups.len()
                    ).into());
                }

                for (lookup_idx, (custom_lookup, official_lookup)) in custom_lookups
                    .iter()
                    .zip(official_lookups.iter())
                    .enumerate()
                {
                    validate_address_lookup_equality(
                        custom_lookup,
                        official_lookup,
                        filename,
                        entry_idx,
                        tx_idx,
                        lookup_idx,
                    )?;
                    total_lookups += 1;
                }
            }
        }
    }

    Ok(total_lookups)
}

fn validate_pubkey_serialization(
    data: &[u8],
    filename: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            let (custom_keys, official_keys) =
                extract_account_keys(&custom_tx.message, &official_tx.message)?;

            for (custom_key, official_key) in custom_keys.iter().zip(official_keys.iter()) {
                let custom_serialized = bincode::serialize(custom_key)?;
                let official_serialized = bincode::serialize(official_key)?;

                if custom_serialized != official_serialized {
                    return Err(format!("Pubkey serialization mismatch in {}", filename).into());
                }
            }
        }
    }

    Ok(())
}

fn validate_pubkey_bytes(data: &[u8], filename: &str) -> Result<(), Box<dyn std::error::Error>> {
    let custom_entries: Vec<CustomEntry> = bincode::deserialize(data)?;
    let official_entries: Vec<OfficialEntry> = bincode::deserialize(data)?;

    for (custom_entry, official_entry) in custom_entries.iter().zip(official_entries.iter()) {
        for (custom_tx, official_tx) in custom_entry
            .transactions
            .iter()
            .zip(official_entry.transactions.iter())
        {
            let (custom_keys, official_keys) =
                extract_account_keys(&custom_tx.message, &official_tx.message)?;

            for (custom_key, official_key) in custom_keys.iter().zip(official_keys.iter()) {
                if custom_key.as_ref() != official_key.as_ref() {
                    return Err(
                        format!("Pubkey byte representation mismatch in {}", filename).into(),
                    );
                }
            }
        }
    }

    Ok(())
}

fn extract_account_keys<'a>(
    custom_msg: &'a CustomMessage,
    official_msg: &'a OfficialMessage,
) -> Result<(&'a Vec<CustomPubkey>, &'a Vec<OfficialPubkey>), Box<dyn std::error::Error>> {
    match (custom_msg, official_msg) {
        (CustomMessage::Legacy(custom), OfficialMessage::Legacy(official)) => {
            Ok((&custom.account_keys, &official.account_keys))
        }
        (CustomMessage::V0(custom), OfficialMessage::V0(official)) => {
            Ok((&custom.account_keys, &official.account_keys))
        }
        _ => Err("Message type mismatch".into()),
    }
}

fn extract_address_lookups<'a>(
    custom_msg: &'a CustomMessage,
    official_msg: &'a OfficialMessage,
) -> Result<
    (
        Option<&'a Vec<CustomLookup>>,
        Option<&'a Vec<OfficialLookup>>,
    ),
    Box<dyn std::error::Error>,
> {
    match (custom_msg, official_msg) {
        (CustomMessage::Legacy(_), OfficialMessage::Legacy(_)) => Ok((None, None)),
        (CustomMessage::V0(custom), OfficialMessage::V0(official)) => Ok((
            Some(&custom.address_table_lookups),
            Some(&official.address_table_lookups),
        )),
        _ => Err("Message type mismatch".into()),
    }
}

fn validate_pubkey_equality(
    custom: &CustomPubkey,
    official: &OfficialPubkey,
    filename: &str,
    entry_idx: usize,
    tx_idx: usize,
    key_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    if custom.as_ref() != official.as_ref() {
        return Err(format!(
            "Pubkey mismatch in {} entry {} tx {} key {}",
            filename, entry_idx, tx_idx, key_idx
        )
        .into());
    }

    Ok(())
}

fn validate_address_lookup_equality(
    custom: &CustomLookup,
    official: &OfficialLookup,
    filename: &str,
    entry_idx: usize,
    tx_idx: usize,
    lookup_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    if custom.account_key.as_ref() != official.account_key.as_ref() {
        return Err(format!(
            "Address lookup account_key mismatch in {} entry {} tx {} lookup {}",
            filename, entry_idx, tx_idx, lookup_idx
        )
        .into());
    }

    if custom.writable_indexes != official.writable_indexes {
        return Err(format!(
            "Address lookup writable_indexes mismatch in {} entry {} tx {} lookup {}",
            filename, entry_idx, tx_idx, lookup_idx
        )
        .into());
    }

    if custom.readonly_indexes != official.readonly_indexes {
        return Err(format!(
            "Address lookup readonly_indexes mismatch in {} entry {} tx {} lookup {}",
            filename, entry_idx, tx_idx, lookup_idx
        )
        .into());
    }

    Ok(())
}
