#!/bin/bash

echo "🧪 Running Comprehensive Solana Data Structure Tests"
echo "=================================================="
echo ""

# Check if test data exists
if [ ! -d "test/raw_shreds" ]; then
    echo "❌ Test data not found. Please run ./collect_shreds.sh first to collect test data."
    exit 1
fi

# Count test data files
SHRED_COUNT=$(ls test/raw_shreds/*.bin 2>/dev/null | wc -l)
echo "📊 Found $SHRED_COUNT shred test files"
echo ""

# Run individual module tests
echo "🔍 Testing Entries Module..."
cargo test test_entries --lib -- --nocapture

echo ""
echo "🔍 Testing Transactions Module..."
cargo test test_transactions --lib -- --nocapture

echo ""
echo "🔍 Testing Messages Module..."
cargo test test_messages --lib -- --nocapture

echo ""
echo "🔍 Testing Instructions Module..."
cargo test test_instructions --lib -- --nocapture

echo ""
echo "🔍 Testing Accounts Module..."
cargo test test_accounts --lib -- --nocapture

echo ""
echo "🔍 Testing Integration Pipeline..."
cargo test test_integration --lib -- --nocapture

echo ""
echo "📈 Running Performance Tests..."
cargo test test_performance --lib -- --nocapture

echo ""
echo "✅ All tests completed!"
echo ""
echo "📋 Test Summary:"
echo "- Binary compatibility tests for all custom types"
echo "- Field-by-field validation against official Solana types"
echo "- Edge case testing (smallest/largest files)"
echo "- Integration testing of full decode pipeline"
echo "- WASM function compatibility verification"
echo "- Performance comparison measurements"
echo ""
echo "🎯 Goal: Prove 100% functional equivalence with official Solana types"
