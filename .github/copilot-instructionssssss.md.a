---
applyTo: '**'
---

# Project Coding Guidelines

This document provides comprehensive coding standards for AI assistants working on this project.

## Project Overview

This project (`helio-payment-tracker`) is a Rust CLI application that listens to Solana blockchain transactions containing specific wallet addresses for Helio payment processing. The application decodes transactions to extract payment information and notifies users via JSON-RPC WebSocket.

## Rust Configuration

### Project-Specific Settings

-   Use Rust edition 2024
-   Follow Rust 2024 idioms and best practices
-   Enable clippy for code quality checks
-   Use rustfmt for consistent formatting

### Code Safety Rules

-   Prefer safe Rust, avoid `unsafe` unless absolutely necessary
-   Use Rust's type system for compile-time safety
-   Handle errors explicitly with `Result<T, E>`
-   Use `Option<T>` for nullable values
-   Prefer owned types over borrowed when crossing async boundaries
-   Use appropriate lifetimes for borrowed data

## Code Formatting

### Indentation & Spacing

-   Use 4 spaces for indentation (rustfmt default)
-   Maximum line length: 100 characters
-   Remove trailing whitespace
-   Add blank line at end of files
-   Use LF (`\n`) line endings
-   Place spaces around operators: `a + b`
-   Add space after commas: `vec![1, 2, 3]`

### Punctuation & Symbols

-   Always use semicolons where required
-   Use double quotes (`"`) for strings
-   Use trailing commas in multiline structures
-   Use "one true brace style": opening brace on same line
-   Closing bracket on new line for multiline structures
-   Use snake_case for variables and functions
-   Use PascalCase for types, structs, enums, and traits

### Line Breaking & Padding

-   Add blank lines before and after major code blocks
-   Add blank line before `return` statements
-   Always blank line before and after: `struct`, `enum`, `impl`, `fn`, `if`, `for`, `while`, `match`, `loop`
-   No blank lines between match arms unless they contain multiple statements
-   Add blank line between variable declarations and method calls

## Import Organization

### Import Order (Strict)

1. Standard library (`std::`, `core::`)
2. External crates (alphabetical)
3. Local modules (by proximity: `crate::`, `super::`, `self::`)

### Import Rules

-   Remove unused imports automatically
-   Keep import statements at top of file
-   Group imports with blank lines between categories
-   Use absolute paths for clarity: `crate::module::Type`
-   Prefer glob imports sparingly, only for prelude modules
-   Use `as` for disambiguation when needed

## Function & Variable Rules

### Function Guidelines

-   Keep return statements clear and explicit
-   Maximum 50 lines per function (recommended)
-   Maximum 3 levels of nesting depth
-   Prefix unused parameters with underscore: `_error`, `_unused`
-   Use descriptive names indicating purpose
-   Prefer `?` operator for error propagation
-   Use early returns to reduce nesting
-   For async functions, use `.await` consistently
-   **Function signatures should be on single lines when possible** for better readability
-   **Prefer explicit return types for public functions** for better API documentation
-   **Use Result<T, E> for fallible operations** - Always return Result for operations that can fail

### Variable Rules

-   Use snake_case for variables and functions
-   Use PascalCase for types, structs, enums, traits
-   Use SCREAMING_SNAKE_CASE for constants and statics
-   Keep function parameters on same line when reasonable
-   Group variable declarations logically
-   Use `let` for mutable bindings, prefer immutable by default

## Naming Conventions

### File & Directory Naming

-   Files: snake_case for regular files
-   Directories: snake_case grouped by functionality
-   Rust files: `.rs` extension

### Code Naming

-   Variables and functions: snake_case
-   Types, structs, enums, traits: PascalCase
-   Constants and statics: SCREAMING_SNAKE_CASE
-   Modules: snake_case
-   Crates: kebab-case

## Code Organization

### File Structure

1. Module attributes (`#![...]`)
2. Imports (following grouping rules)
3. Type definitions and traits
4. Constants and static variables
5. Implementation blocks
6. Functions

### Module Organization

-   Each major feature has its own module
-   Use `mod.rs` or single file modules as appropriate
-   Re-export public items through module root
-   Use `pub use` for convenient API surface
-   Group related functionality in the same module

### Export Patterns

-   Use `pub` for public items
-   Re-export through `pub use` for convenience
-   Organize public API thoughtfully
-   Use `#[doc(hidden)]` for internal but public items

## Error Handling Guidelines

### Error Handling Patterns

-   Use `Result<T, E>` for fallible operations
-   Create custom error types using `thiserror` crate
-   Use `anyhow` for application-level error handling
-   Propagate errors with `?` operator
-   Handle errors at appropriate levels

### Error Types

-   Define domain-specific error types
-   Implement `std::error::Error` trait
-   Use `#[derive(thiserror::Error)]` for custom errors
-   Provide meaningful error messages
-   Chain errors to preserve context

## Async Programming

### Async Guidelines

-   Use `async/await` for asynchronous operations
-   Choose appropriate async runtime (tokio, async-std)
-   Handle async cancellation properly
-   Use channels for communication between tasks
-   Avoid blocking operations in async context

### Concurrency Patterns

-   Use `Arc<Mutex<T>>` for shared mutable state
-   Prefer message passing over shared memory
-   Use appropriate synchronization primitives
-   Handle deadlocks and race conditions

## Build and Development

### Build Process

-   Use `cargo build` for development builds
-   Use `cargo build --release` for optimized builds
-   Use `cargo check` for fast compilation checking
-   Use `cargo clippy` for linting
-   Use `cargo fmt` for formatting

### Development Scripts

-   `cargo run` - Run the application
-   `cargo clippy` - Lint code
-   `cargo fmt` - Format code
-   `cargo clean` - Clean build artifacts

## Code Quality Guidelines

### Performance Considerations

-   Use appropriate data structures for the use case
-   Avoid unnecessary allocations
-   Use iterators efficiently
-   Profile before optimizing
-   Consider zero-copy operations when possible

### Memory Management

-   Understand ownership and borrowing
-   Use `Rc<T>` and `Arc<T>` for shared ownership
-   Avoid memory leaks with proper cleanup
-   Use `Box<T>` for heap allocation when needed

## Implementation Guidelines

### Scope Limitation Rule

-   **Only implement exactly what is requested in the task**
-   Do not create additional code, features, or functionality beyond the explicit requirements
-   If you think there might be related improvements or additions, ask the user first before implementing
-   Focus on the specific requirements and avoid scope creep
-   Stick to the minimal viable implementation that satisfies the request

### Default Exclusions Rule

By default, **skip the following unless explicitly requested**:

-   **Input validation and parameter checking** - Only add when specifically asked
-   **Documentation** - Comments, doc comments, README updates, or inline documentation
-   **Example implementations** - Only create when the user asks for examples
-   **Comprehensive error handling** - Only add when specifically requested

**Important**: Only create these items when the user specifically asks for them in their request. This keeps implementations focused and prevents unnecessary work.

### Code Documentation Rule

-   **Never generate comments in source code by default** - Code should be self-documenting through clear naming and structure
-   Only add comments when explicitly requested or when code behavior is genuinely non-obvious
-   Use doc comments (`///`) for public API documentation only when requested
-   Prefer refactoring unclear code over adding explanatory comments

### Code Quality Checks Rule

-   **Never run automated checks unless explicitly requested** - Do not automatically run cargo clippy, cargo check, or other validation tools
-   Only execute `cargo clippy`, `cargo check`, or similar commands when the user specifically asks for them
-   Focus on implementing the requested changes without automatic validation unless verification is requested
-   Let the user decide when to run quality checks and validation

## Code Reuse Guidelines

### Reuse Principles

-   Follow DRY (Don't Repeat Yourself) principle
-   Extract common functionality into modules
-   Use traits for shared behavior
-   Prefer composition over inheritance (use traits and structs)

### Avoiding Duplication

-   Apply Rule of Three: if code is copy-pasted 3 times, extract it into reusable function
-   Search existing code before implementing new functionality
-   Use macros sparingly and only for code generation

### Maintainability

-   Keep functions focused on single responsibility
-   Use meaningful variable and function names
-   Maintain consistent code organization across modules
-   Use Rust's type system to prevent runtime errors
-   Document public APIs with doc comments when requested

## Specific Project Guidelines

### gRPC Integration

-   Generate clients from proto files using build scripts
-   Handle connection failures gracefully
-   Implement proper retry logic for streaming connections
-   Use async streams for real-time data processing

### Blockchain Integration

-   Handle Solana transaction parsing correctly
-   Validate transaction data before processing
-   Implement proper error handling for blockchain operations
-   Use appropriate data structures for blockchain data

### WebSocket Communication

-   Implement proper WebSocket error handling
-   Handle client disconnections gracefully
-   Use JSON-RPC format for messages
-   Implement proper message queuing if needed

### Configuration Management

-   Use structured configuration (TOML, JSON, or environment variables)
-   Validate configuration on startup
-   Provide clear error messages for invalid configuration
-   Support configuration reloading when appropriate
