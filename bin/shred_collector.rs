use solana_stream_sdk::{CommitmentLevel, ShredstreamClient};
use std::fs;
use std::path::Path;

const TARGET_SHRED_COUNT: usize = 1000;
const OUTPUT_DIR: &str = "test/raw_shreds";

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenvy::dotenv().ok();

    let endpoints = vec![
        "https://shreds-far-point.erpc.global",
        "https://shreds-ams-9.erpc.global",
        "https://shreds-ny-1.erpc.global",
    ];

    let mut client = None;
    let mut used_endpoint = "";

    for endpoint in &endpoints {
        println!("Trying to connect to: {}", endpoint);
        match ShredstreamClient::connect(endpoint).await {
            Ok(c) => {
                client = Some(c);
                used_endpoint = endpoint;
                println!("Successfully connected to: {}", endpoint);
                break;
            }
            Err(e) => {
                println!("Failed to connect to {}: {}", endpoint, e);
                continue;
            }
        }
    }

    let mut client = client.ok_or("Failed to connect to any endpoint")?;

    let request = ShredstreamClient::create_entries_request_for_accounts(
        vec![],
        vec![],
        vec![],
        Some(CommitmentLevel::Processed),
    );

    let mut stream = client.subscribe_entries(request).await?;
    let mut shred_count = 0;

    println!("Starting shred collection...");
    println!("Target: {} shreds", TARGET_SHRED_COUNT);

    while let Some(slot_entry) = stream.message().await? {
        if shred_count >= TARGET_SHRED_COUNT {
            break;
        }

        shred_count += 1;

        let filename = format!("shred_{:03}.bin", shred_count);
        let filepath = Path::new(OUTPUT_DIR).join(&filename);

        fs::write(&filepath, &slot_entry.entries)?;

        println!(
            "Collected shred {}/{} - Slot: {} - Size: {} bytes",
            shred_count,
            TARGET_SHRED_COUNT,
            slot_entry.slot,
            slot_entry.entries.len()
        );
    }

    println!(
        "Collection completed! {} shreds saved to {}/",
        shred_count, OUTPUT_DIR
    );
    Ok(())
}
