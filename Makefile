.PHONY: build build-release build-wasm clean fmt lint test test-all collect-shreds analyze-shreds help

# Default target
help:
	@echo "Shredstream Decoder - Available Commands:"
	@echo ""
	@echo "Build Commands:"
	@echo "  build          - Build the project in debug mode"
	@echo "  build-release  - Build the project in release mode"
	@echo "  build-wasm     - Build WASM package with TypeScript definitions"
	@echo ""
	@echo "Development Commands:"
	@echo "  fmt            - Format code with rustfmt"
	@echo "  lint           - Run clippy linter"
	@echo "  clean          - Clean build artifacts"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test           - Run basic tests"
	@echo "  test-all       - Run comprehensive test suite"
	@echo "  collect-shreds - Collect shred data for testing"
	@echo "  analyze-shreds - Analyze collected shred data"
	@echo ""
	@echo "Environment:"
	@echo "  Make sure to set SHREDSTREAM_ENDPOINT in .env file"

# Build commands
build:
	cargo build

build-release:
	cargo build --release

build-wasm:
	@echo "Building WASM package with TypeScript definitions..."
	wasm-pack build --target bundler --out-dir pkg
	@echo "🔧 Adding missing TypeScript types..."
	@if [ -f pkg/shredstream_decoder.d.ts ]; then \
		sed -i.bak '/export type Pubkey = number\[\];/a\
\
export type Signature = number[];' pkg/shredstream_decoder.d.ts && \
		rm pkg/shredstream_decoder.d.ts.bak; \
	fi
	@echo "✅ Build complete!"
	@echo "📁 Output directory: pkg/"
	@echo "📄 TypeScript definitions: pkg/shredstream_decoder.d.ts"

# Development commands
fmt:
	cargo fmt

lint:
	cargo clippy -- -D warnings

clean:
	cargo clean
	rm -rf pkg/
	rm -rf target/

# Testing commands
test:
	cargo test

test-all:
	@echo "🧪 Running Comprehensive Solana Data Structure Tests"
	@echo "=================================================="
	@echo ""
	@if [ ! -d "tests/data" ]; then \
		echo "❌ Test data not found. Please run 'make collect-shreds' first."; \
		exit 1; \
	fi
	@SHRED_COUNT=$$(ls tests/data/*.bin 2>/dev/null | wc -l | tr -d ' '); \
	echo "📊 Found $$SHRED_COUNT shred test files"; \
	echo ""
	@echo "🔍 Testing Entries Module..."
	cargo test test_entries --lib -- --nocapture
	@echo ""
	@echo "🔍 Testing Transactions Module..."
	cargo test test_transactions --lib -- --nocapture
	@echo ""
	@echo "🔍 Testing Messages Module..."
	cargo test test_messages --lib -- --nocapture
	@echo ""
	@echo "🔍 Testing Instructions Module..."
	cargo test test_instructions --lib -- --nocapture
	@echo ""
	@echo "🔍 Testing Accounts Module..."
	cargo test test_accounts --lib -- --nocapture
	@echo ""
	@echo "🔍 Testing Integration Pipeline..."
	cargo test test_integration --lib -- --nocapture
	@echo ""
	@echo "✅ All tests completed!"

collect-shreds:
	@echo "Starting Solana Shred Data Collection..."
	@echo "This will collect shred samples from the shredstream."
	@echo ""
	@if [ ! -f .env ]; then \
		echo "❌ .env file not found. Please create it with SHREDSTREAM_ENDPOINT."; \
		exit 1; \
	fi
	cargo run --bin shred_collector --features "tokio,solana-stream-sdk,dotenvy"
	@echo ""
	@echo "Collection completed. Check tests/data/ directory for the collected data."

analyze-shreds:
	@echo "Analyzing collected shred data..."
	cargo test analyze_shreds --test analyze_shreds -- --nocapture
